# C-Note Validation Toggle Implementation

## Overview
This implementation adds an optional toggle for C-Note validation in cash-entry.php and credit-entry.php. When disabled, users can enter any docket number without validation against the cn_entries table.

## Files Modified

### 1. Database Schema
**File:** `sql/add_cnote_validation_column.sql`
- Adds `cnote_validation_enabled` column to settings table
- Default value: 1 (enabled) to maintain current behavior
- Data type: TINYINT(1)

### 2. Settings Page UI
**File:** `pages/settings.php`
- Added toggle switch CSS styles
- Added "Validation Settings" section with C-Note validation toggle
- Added JavaScript to update toggle status text
- Updated default settings array to include new field

### 3. Settings Processing
**File:** `pages/update_settings.php`
- Added handling for `cnote_validation_enabled` field
- Updated both INSERT and UPDATE queries
- Added parameter binding for new field

### 4. Cash Entry Validation
**File:** `process/process_cash_entry.php`
- Added settings check before validation
- Made docket validation conditional based on user setting
- Maintains backward compatibility

### 5. Credit Entry Validation
**File:** `process/process_credit_entry.php`
- Added settings check for single entry validation
- Added settings check for bulk upload validation
- Made both CN existence and customer matching conditional

### 6. AJAX Validation Files
**File:** `process/validate_docket.php`
- Added settings check before validation
- Returns 'valid' when validation is disabled

**File:** `process/validate_docket_customer.php`
- Added settings check before validation
- Returns valid JSON response when validation is disabled

## Implementation Details

### Database Changes
```sql
ALTER TABLE `settings` 
ADD COLUMN `cnote_validation_enabled` TINYINT(1) NOT NULL DEFAULT 1 
COMMENT 'Enable/Disable C-Note validation (1=enabled, 0=disabled)' 
AFTER `ecom_surface_docket`;
```

### Settings UI
- Modern toggle switch design
- Real-time status text updates
- Consistent with existing UI styling
- Clear description of functionality

### Validation Logic
- **When Enabled (default):** Current behavior maintained
- **When Disabled:** All docket validations bypassed
- **Scope:** Affects both cash and credit entries, single and bulk

### Backward Compatibility
- Default value ensures existing behavior
- No breaking changes to existing functionality
- Graceful fallback if setting not found

## Testing Checklist

### Database
- [ ] Run SQL script to add column
- [ ] Verify column exists with correct default value
- [ ] Test with existing users

### Settings Page
- [ ] Toggle switch displays correctly
- [ ] Status text updates when toggled
- [ ] Form submission saves setting correctly
- [ ] Reset button works properly

### Cash Entry
- [ ] With validation enabled: Current behavior maintained
- [ ] With validation disabled: Any docket number accepted
- [ ] Error handling works correctly

### Credit Entry
- [ ] Single entry: Validation respects setting
- [ ] Bulk upload: Validation respects setting
- [ ] Customer matching respects setting

### AJAX Validation
- [ ] validate_docket.php respects setting
- [ ] validate_docket_customer.php respects setting
- [ ] Frontend validation updates accordingly

## Usage Instructions

1. **Enable/Disable Validation:**
   - Go to Settings page
   - Find "Validation Settings" section
   - Toggle "C-Note Validation" switch
   - Click "Save Changes"

2. **When Disabled:**
   - Any docket number can be entered
   - No validation against cn_entries table
   - No customer matching validation
   - Applies to both cash and credit entries

3. **When Enabled:**
   - Current validation behavior maintained
   - Docket must exist in cn_entries
   - Customer matching enforced for credit entries

## Security Considerations
- Input sanitization maintained
- User authentication still required
- Setting is per-user (not global)
- Validation can be re-enabled at any time

## Performance Impact
- Minimal: One additional database query per validation check
- Settings are fetched per request (could be cached in future)
- No impact when validation is enabled (current behavior)

## Future Enhancements
- Cache settings to reduce database queries
- Add validation bypass for specific docket patterns
- Add audit logging for validation bypasses
- Add bulk toggle for multiple users (admin feature)
