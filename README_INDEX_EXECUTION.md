# Database Index Execution Guide

## 📋 Files Created for phpMyAdmin Execution

Execute these SQL files **in order** in phpMyAdmin:

### 1. **01_cn_entries_indexes.sql**
- Creates indexes for `cn_entries` table
- Improves CN number fetching performance
- **Execute first**

### 2. **02_customers_indexes.sql** 
- Creates indexes for `customers` table
- Improves customer name fetching performance
- **Execute second**

### 3. **03_settings_indexes.sql**
- Creates indexes for `settings` table  
- Improves settings retrieval performance
- **Execute third**

### 4. **04_transactions_indexes.sql**
- Creates indexes for `transactions` table
- Improves general transaction queries
- **Execute fourth**

### 5. **05_verify_indexes.sql**
- Verification queries to check if indexes were created
- **Execute last to verify**

## 🚀 How to Execute in phpMyAdmin

1. **Login to phpMyAdmin**
2. **Select your database**
3. **Go to SQL tab**
4. **Copy and paste** content from each file **one by one**
5. **Click "Go"** to execute
6. **Repeat** for all 5 files in order

## ⚠️ Important Notes

- **Backup your database** before executing
- Execute files **in the specified order**
- If you get "index already exists" error, it's safe to ignore
- **Test page performance** after all indexes are created
- The **docket_no index is excluded** as requested

## 📊 Expected Results

After creating these indexes, you should see:
- **Faster page loading** for cash-entry.php and credit-entry.php
- **Reduced database query time**
- **Better overall application performance**

## 🔍 Performance Testing

Before and after creating indexes, test:
- Page load time for cash-entry.php
- Page load time for credit-entry.php
- Time to populate dropdown lists
