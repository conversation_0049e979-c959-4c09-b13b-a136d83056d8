<?php
include '../db_connect.php';

$customer = $_GET['customer'];
$mode_of_tsp = $_GET['mode_of_tsp'];
$zone = $_GET['zone'];
$weight = floatval($_GET['weight']);

echo "Received Data: $customer | $mode_of_tsp | $zone | $weight <br>"; // Debug Line 1

if ($customer != "" && $zone != "" && $weight != "" && $mode_of_tsp != "") {
    $sql = "SELECT * FROM rate_master WHERE short_name = ? AND mode_of_tsp = ? AND zone = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $customer, $mode_of_tsp, $zone);
    $stmt->execute();
    $result = $stmt->get_result();
    $rate = $result->fetch_assoc();

    if ($rate) {
        echo "Rate Found ✅ <br>"; // Debug Line 2
        print_r($rate); // Debug Line 3

        $amount = 0;
        if ($weight <= 0.250) {
            $amount = $rate['up_to_0250'];
        } elseif ($weight <= 0.500) {
            $amount = $rate['up_to_0500'];
        } elseif ($weight <= 3) {
            $extraWeight = ceil(($weight - 0.500) / 0.500);
            $amount = $rate['up_to_0500'] + ($extraWeight * $rate['addl_500gm']);
        } else {
            $amount = $rate['up_to_0500'] + (5 * $rate['addl_500gm']) + (ceil($weight - 3) * $rate['above_3kg']);
        }

        echo "Final Amount: $amount <br>"; // Debug Line 4
        echo round($amount);
    } else {
        echo "Rate Not Found ❌";
    }
}
?>
