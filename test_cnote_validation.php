<?php
// Test script to verify C-Note validation toggle functionality
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    die("Please log in first");
}

$username = $_SESSION['username'];

echo "<h2>C-Note Validation Toggle Test</h2>";

// Test 1: Check if column exists
echo "<h3>Test 1: Database Column Check</h3>";
$result = $conn->query("DESCRIBE settings");
$columns = [];
while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
}

if (in_array('cnote_validation_enabled', $columns)) {
    echo "✅ Column 'cnote_validation_enabled' exists in settings table<br>";
} else {
    echo "❌ Column 'cnote_validation_enabled' does NOT exist in settings table<br>";
    echo "Please run the SQL script: sql/add_cnote_validation_column.sql<br>";
}

// Test 2: Check current setting value
echo "<h3>Test 2: Current Setting Value</h3>";
$stmt = $conn->prepare("SELECT cnote_validation_enabled FROM settings WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $data = $result->fetch_assoc();
    $validation_enabled = $data['cnote_validation_enabled'];
    echo "Current C-Note validation setting: " . ($validation_enabled ? "Enabled" : "Disabled") . "<br>";
} else {
    echo "No settings found for user: $username<br>";
}

// Test 3: Test validation files
echo "<h3>Test 3: Validation Files Test</h3>";

// Test validate_docket.php
echo "<strong>Testing validate_docket.php:</strong><br>";
$test_docket = "TEST123";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost/process/validate_docket.php");
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, "docket_no=" . $test_docket);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
$response = curl_exec($ch);
curl_close($ch);

if ($response) {
    echo "Response: " . $response . "<br>";
} else {
    echo "Could not test validate_docket.php (requires web server)<br>";
}

echo "<br><strong>Implementation Status:</strong><br>";
echo "✅ Settings UI updated<br>";
echo "✅ Database handling updated<br>";
echo "✅ Validation logic updated<br>";
echo "✅ Process files updated<br>";

echo "<br><strong>Next Steps:</strong><br>";
echo "1. Run the SQL script to add the database column<br>";
echo "2. Test the settings page toggle<br>";
echo "3. Test cash entry with validation disabled<br>";
echo "4. Test credit entry with validation disabled<br>";

$conn->close();
?>
