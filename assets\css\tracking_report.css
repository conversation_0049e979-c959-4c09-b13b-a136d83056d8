/* Critical styles */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow: auto;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

#mainContent {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in;
    min-height: 100vh;
    width: 100%;
    position: relative;
    z-index: 1;
}

.report-container {
    padding: 2rem;
    margin: 0 auto;
    max-width: 1400px;
    position: relative;
    margin-left: 0px;
    margin-top: 0px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Root variables */
:root {
    --primary-blue: #2196F3;
    --light-blue: #E3F2FD;
    --hover-blue: #1976D2;
    --sky-blue: #87CEEB;
    --text-dark: #2c3e50;
    --border-color: #e0e0e0;
    --background: #F8FAFC;
}

/* Header styles */
.header-section {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.header-section1 {
    background: white;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    color: var(--primary-blue);
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0;
}

/* Button styles */
.btn-download, .btn-upload {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 1px solid transparent;
    cursor: pointer;
}

.btn-download:hover, .btn-upload:hover {
    background: var(--primary-blue);
    color: white;
}

/* Search styles */
.search-section {
    flex: 1;
    max-width: 500px;
    min-width: 280px;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-box {
    width: 100%;
    padding: 0.75rem 3.5rem 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: var(--background);
}

.search-box:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: white;
    box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
}

.clear-search {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: var(--light-blue);
    color: var(--primary-blue);
    cursor: pointer;
    padding: 0.4rem 0.8rem;
    display: none;
    font-size: 0.85rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.clear-search:hover {
    background: var(--primary-blue);
    color: white;
}

.clear-search:active {
    transform: translateY(-50%) scale(0.95);
}

.total-badge {
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Table styles */
.data-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-top: 1rem;
    max-height: calc(100vh - 400px);
    display: flex;
    flex-direction: column;
}

.table-container {
    overflow-x: auto;
    overflow-y: auto;
    flex: 1;
    scroll-behavior: smooth;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    min-width: 1200px;
}

.table thead {
    position: sticky;
    top: 0;
    z-index: 10;
}

.table thead th {
    background: var(--primary-blue);
    color: white;
    padding: 0.8rem;
    font-weight: 500;
    text-align: left;
    font-size: 0.85rem;
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.table thead th:hover {
    background: var(--hover-blue);
}

.table thead th::after {
    content: '↕';
    position: absolute;
    right: 8px;
    opacity: 0.5;
}

.table thead th.sort-asc::after {
    content: '↑';
    opacity: 1;
}

.table thead th.sort-desc::after {
    content: '↓';
    opacity: 1;
}

.table tbody td {
    padding: 0.6rem 0.8rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-dark);
    font-size: 0.85rem;
    white-space: nowrap;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr:hover {
    background: var(--background);
}

/* Pagination styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    background: white;
    border-top: 1px solid var(--border-color);
    gap: 0.5rem;
}

.pagination-button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.pagination-button:hover:not(:disabled) {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-button.active {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

/* Alert styles */
.success-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    background-color: #4CAF50;
    color: white;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    animation: slideInRight 0.5s ease-out;
    font-size: 16px;
    font-weight: 500;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.success-alert.show {
    display: block;
}

/* Action buttons */
.action-btns {
    display: flex;
    gap: 0.5rem;
    white-space: nowrap;
}

.btn-update {
    padding: 0.4rem 0.75rem;
    font-size: 0.85rem;
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-update:hover {
    background: var(--primary-blue);
    color: white;
}

/* Loading indicator */
.loading-indicator {
    display: none;
    width: 24px;
    height: 24px;
    border: 2px solid var(--light-blue);
    border-radius: 50%;
    border-top: 2px solid var(--primary-blue);
    animation: spin 0.8s linear infinite;
    position: absolute;
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
}

/* Filter styles */
.filters-section {
    width: 100%;
    margin-bottom: 1.5rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 500;
}

.filter-input {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: var(--background);
}

.filter-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: white;
    box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

.btn-apply, .btn-reset {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    text-align: center;
}

.btn-apply {
    background: var(--primary-blue);
    color: white;
    border: none;
}

.btn-apply:hover {
    background: var(--hover-blue);
}

.btn-reset {
    background: var(--light-blue);
    color: var(--primary-blue);
    border: 1px solid transparent;
}

.btn-reset:hover {
    background: var(--primary-blue);
    color: white;
}

.btn-download {
    background: #217346;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    text-align: center;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-download:hover {
    background: #1a5c38;
    color: white;
}

/* Editable cell styles */
.editable-cell {
    position: relative;
}

.editable-content {
    padding: 0.5rem;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.editable-content:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: white;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* Status select styles */
.status-select {
    padding: 0.4rem 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.85rem;
    background: var(--background);
    color: var(--text-dark);
    transition: all 0.2s ease;
}

.status-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: white;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* Responsive styles */
@media (max-width: 1024px) {
    .report-container {
        padding: 1rem;
    }

    .header-section {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .header-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-section {
        order: 2;
    }

    .total-badge {
        align-self: flex-start;
    }

    .table thead {
        display: none;
    }

    .table tbody td {
        display: block;
        padding: 0.5rem 1rem;
        text-align: right;
        border: none;
    }

    .table tbody tr {
        display: block;
        border-bottom: 1px solid var(--border-color);
        padding: 0.5rem 0;
    }

    .table td::before {
        content: attr(data-label);
        float: left;
        font-weight: 500;
        color: var(--primary-blue);
    }

    .action-btns {
        justify-content: flex-end;
        margin-top: 0.5rem;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
    
    .btn-apply, .btn-reset {
        width: 100%;
    }
} 