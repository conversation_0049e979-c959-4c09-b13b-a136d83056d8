<?php
// Start output buffering to prevent any accidental output
ob_start();

session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Handle CN deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_cns'])) {
    $selected_cns = $_POST['selected_cns'] ?? [];
    $deleted_count = 0;
    $errors = [];

    if (!empty($selected_cns)) {
        foreach ($selected_cns as $cn_number) {
            try {
                // Check if CN belongs to current user
                $check_stmt = $conn->prepare("SELECT id FROM cn_entries WHERE cn_number = ? AND username = ?");
                $check_stmt->bind_param("ss", $cn_number, $username);
                $check_stmt->execute();
                $result = $check_stmt->get_result();

                if ($result->num_rows > 0) {
                    // Delete the CN
                    $delete_stmt = $conn->prepare("DELETE FROM cn_entries WHERE cn_number = ? AND username = ?");
                    $delete_stmt->bind_param("ss", $cn_number, $username);

                    if ($delete_stmt->execute()) {
                        $deleted_count++;
                    } else {
                        $errors[] = "Failed to delete CN: " . $cn_number;
                    }
                    $delete_stmt->close();
                } else {
                    $errors[] = "CN not found or access denied: " . $cn_number;
                }
                $check_stmt->close();
            } catch (Exception $e) {
                $errors[] = "Error deleting CN " . $cn_number . ": " . $e->getMessage();
            }
        }

        // Set success/error messages
        if ($deleted_count > 0) {
            $_SESSION['success_message'] = "Successfully deleted $deleted_count CN(s).";
        }
        if (!empty($errors)) {
            $_SESSION['error_message'] = implode("; ", $errors);
        }
    } else {
        $_SESSION['error_message'] = "No CNs selected for deletion.";
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['REQUEST_URI']);
    exit;
}

// Handle Excel export - must be done before any HTML output
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    // Clear all output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    $export_filters = [
        'cn_type' => $_GET['cn_type'] ?? '',
        'status' => $_GET['status'] ?? '',
        'cn_series' => $_GET['cn_series'] ?? '',
        'search' => $_GET['search'] ?? '',
        'usage' => $_GET['usage'] ?? 'open',
        'expiry_days' => $_GET['expiry_days'] ?? ''
    ];

    // Remove empty filters but keep 'usage' filter
    $export_filters = array_filter($export_filters, function($value, $key) {
        if ($key === 'usage') {
            return true; // Always keep usage filter
        }
        return $value !== '';
    }, ARRAY_FILTER_USE_BOTH);

    try {
        exportToExcel($conn, $username, $export_filters);
    } catch (Exception $e) {
        error_log("Export error: " . $e->getMessage());
        header('Content-Type: text/plain');
        echo "Error generating export: " . $e->getMessage();
    }
    exit;
}

// Get current page and filters from URL
$current_page = (int)($_GET['cn_page'] ?? 1);
$filters = [
    'cn_type' => $_GET['cn_type'] ?? '',
    'status' => $_GET['status'] ?? '',
    'cn_series' => $_GET['cn_series'] ?? '',
    'search' => $_GET['search'] ?? '',
    'usage' => $_GET['usage'] ?? 'open',
    'expiry_days' => $_GET['expiry_days'] ?? ''
];

// Remove empty filters but keep 'usage' filter even if it's 'open' (default)
$filters = array_filter($filters, function($value, $key) {
    if ($key === 'usage') {
        return true; // Always keep usage filter
    }
    return $value !== '';
}, ARRAY_FILTER_USE_BOTH);

// Get available CN series for the dropdown
$available_series = getAvailableCNSeries($conn, $username);

// Get summary statistics
$summary_stats = getSummaryStats($conn, $username);

// Get table data
try {
    $table_data = getAvailableCNs($conn, $username, $current_page, $filters);
} catch (Exception $e) {
    error_log("Error in getAvailableCNs: " . $e->getMessage());
    $table_data = [
        'success' => false,
        'error' => $e->getMessage(),
        'data' => [],
        'pagination' => ['total_records' => 0]
    ];
}

// Functions
function getAvailableCNSeries($conn, $username) {
    try {
        $sql = "
            SELECT
                CASE
                    WHEN LEFT(ce.cn_number, 1) REGEXP '[0-9]' THEN LEFT(ce.cn_number, 2)
                    ELSE LEFT(ce.cn_number, 1)
                END as series,
                COUNT(*) as count
            FROM cn_entries ce
            WHERE ce.username = ?
            AND NOT EXISTS (
                SELECT 1 FROM transactions t
                WHERE t.docket_no = ce.cn_number AND t.username = ?
            )
            GROUP BY
                CASE
                    WHEN LEFT(ce.cn_number, 1) REGEXP '[0-9]' THEN LEFT(ce.cn_number, 2)
                    ELSE LEFT(ce.cn_number, 1)
                END
            ORDER BY series
        ";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        $stmt->bind_param("ss", $username, $username);
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        $series = [];

        while ($row = $result->fetch_assoc()) {
            $series[] = [
                'series' => $row['series'],
                'count' => $row['count']
            ];
        }

        return [
            'success' => true,
            'data' => $series
        ];
    } catch (Exception $e) {
        error_log("getAvailableCNSeries error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => []
        ];
    }
}

function getSummaryStats($conn, $username, $user_filter = null) {
    try {
        $where_clause = '';
        $params = [$username];

        if ($user_filter && $user_filter !== 'all') {
            $where_clause = 'AND ce.username = ?';
            $params[] = $user_filter;
        }

        $sql = "
            SELECT
                COUNT(*) as total_cns,
                COUNT(CASE WHEN t.docket_no IS NULL THEN 1 END) as total_available,
                COUNT(CASE WHEN t.docket_no IS NOT NULL THEN 1 END) as total_used,
                COUNT(CASE WHEN t.docket_no IS NULL AND ce.cn_expiry_date < CURDATE() THEN 1 END) as expired,
                COUNT(CASE WHEN t.docket_no IS NULL AND DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 10 THEN 1 END) as expiring_soon,
                COUNT(CASE WHEN t.docket_no IS NULL AND DATEDIFF(ce.cn_expiry_date, CURDATE()) > 10 THEN 1 END) as valid
            FROM cn_entries ce
            LEFT JOIN transactions t ON ce.cn_number = t.docket_no
            WHERE ce.username = ? $where_clause
        ";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        $result = $stmt->get_result()->fetch_assoc();

        return [
            'success' => true,
            'data' => $result
        ];
    } catch (Exception $e) {
        error_log("getSummaryStats error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function getAvailableCNs($conn, $username, $page = 1, $filters = []) {
    try {
        $limit = 25;
        $offset = ($page - 1) * $limit;
        $where_conditions = ["ce.username = ?"];
        $params = [$username];

        // Handle search and usage filter logic
        $usage_filter = $filters['usage'] ?? 'open';
        $has_search = !empty($filters['search']);

        // If searching, ignore usage filter and search all CNs
        // If not searching, apply usage filter
        if (!$has_search) {
            if ($usage_filter === 'used') {
                $where_conditions[] = "t.docket_no IS NOT NULL";
            } else {
                // Default: show only open CNs
                $where_conditions[] = "t.docket_no IS NULL";
            }
        }

    // Apply filters
    if (!empty($filters['cn_type'])) {
        $where_conditions[] = 'ce.cn_type = ?';
        $params[] = $filters['cn_type'];
    }

    if (!empty($filters['status'])) {
        switch ($filters['status']) {
            case 'expired':
                $where_conditions[] = 'ce.cn_expiry_date < CURDATE()';
                break;
            case 'expiring':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 10';
                break;
            case 'valid':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) > 10';
                break;
        }
    }

    if (!empty($filters['expiry_days'])) {
        switch ($filters['expiry_days']) {
            case 'expired':
                $where_conditions[] = 'ce.cn_expiry_date < CURDATE()';
                break;
            case '0-10':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 10';
                break;
            case '11-30':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 11 AND 30';
                break;
            case '31-90':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 31 AND 90';
                break;
            case '90+':
                $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) > 90';
                break;
        }
    }

    if (!empty($filters['cn_series'])) {
        $where_conditions[] = '(CASE WHEN LEFT(ce.cn_number, 1) REGEXP "[0-9]" THEN LEFT(ce.cn_number, 2) ELSE LEFT(ce.cn_number, 1) END) = ?';
        $params[] = $filters['cn_series'];
    }

    if (!empty($filters['search'])) {
        // Search across all CNs (both open and used) regardless of usage filter
        // Search in CN number, allocated customer, and transaction customer
        $where_conditions[] = '(ce.cn_number LIKE ? OR ce.customer LIKE ? OR t.customer LIKE ?)';
        $search_term = '%' . $filters['search'] . '%';
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }

    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM cn_entries ce LEFT JOIN transactions t ON ce.cn_number = t.docket_no $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param(str_repeat('s', count($params)), ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];

    // Get data
    $sql = "
        SELECT
            ce.cn_number,
            ce.cn_date as purchase_date,
            ce.cn_expiry_date,
            ce.cn_type,
            ce.username as purchased_by,
            ce.customer as allocated_customer,
            ce.allocation_date,
            DATEDIFF(ce.cn_expiry_date, CURDATE()) as days_until_expiry,
            CASE
                WHEN ce.cn_expiry_date < CURDATE() THEN 'Expired'
                WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) <= 10 THEN 'Expiring Soon'
                ELSE 'Valid'
            END as expiry_status,
            t.docket_date as used_date,
            t.created_at as transaction_created,
            t.customer as transaction_customer,
            CASE
                WHEN t.docket_no IS NOT NULL THEN 'Used'
                ELSE 'Open'
            END as usage_status
        FROM cn_entries ce
        LEFT JOIN transactions t ON ce.cn_number = t.docket_no
        $where_clause
        ORDER BY
            CASE
                WHEN ce.cn_expiry_date < CURDATE() THEN 1
                WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) <= 30 THEN 2
                ELSE 3
            END,
            ce.cn_expiry_date ASC,
            ce.cn_number ASC
        LIMIT ? OFFSET ?
    ";

    $params[] = $limit;
    $params[] = $offset;

    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('s', count($params) - 2) . 'ii', ...$params);
    $stmt->execute();
    $data = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

        return [
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total_records / $limit),
                'total_records' => $total_records,
                'start_record' => $offset + 1,
                'end_record' => min($offset + $limit, $total_records)
            ]
        ];
    } catch (Exception $e) {
        error_log("getAvailableCNs error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => [],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 0,
                'total_records' => 0,
                'start_record' => 0,
                'end_record' => 0
            ]
        ];
    }
}

function formatDate($date) {
    if (empty($date) || $date === '0000-00-00') return '-';
    return date('d-m-Y', strtotime($date));
}

function getStatusBadge($status) {
    $badges = [
        'Valid' => 'success',
        'Expiring Soon' => 'warning',
        'Expired' => 'danger'
    ];
    $class = $badges[$status] ?? 'secondary';
    return "<span class='status-badge status-$class'>$status</span>";
}

function getTypeBadge($type) {
    $class = $type === 'Physical' ? 'primary' : 'info';
    return "<span class='status-badge status-$class'>$type</span>";
}

function getUsageBadge($status) {
    $class = $status === 'Used' ? 'danger' : 'success';
    return "<span class='status-badge status-$class'>$status</span>";
}

function formatUsedDate($row) {
    if ($row['usage_status'] === 'Used') {
        if (!empty($row['used_date'])) {
            return formatDate($row['used_date']);
        } elseif (!empty($row['transaction_created'])) {
            return formatDate($row['transaction_created']);
        }
    }
    return '-';
}

function exportToExcel($conn, $username, $filters = []) {
    // Clear any output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }

    try {
        // Get all data for export (no pagination)
        $where_conditions = ["ce.username = ?"];
        $params = [$username];

        // Handle search and usage filter logic
        $usage_filter = $filters['usage'] ?? 'open';
        $has_search = !empty($filters['search']);

        // If searching, ignore usage filter and search all CNs
        // If not searching, apply usage filter
        if (!$has_search) {
            if ($usage_filter === 'used') {
                $where_conditions[] = "t.docket_no IS NOT NULL";
            } else {
                // Default: show only open CNs
                $where_conditions[] = "t.docket_no IS NULL";
            }
        }

        // Apply filters
        if (!empty($filters['cn_type'])) {
            $where_conditions[] = 'ce.cn_type = ?';
            $params[] = $filters['cn_type'];
        }

        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'expired':
                    $where_conditions[] = 'ce.cn_expiry_date < CURDATE()';
                    break;
                case 'expiring':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 10';
                    break;
                case 'valid':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) > 10';
                    break;
            }
        }

        if (!empty($filters['expiry_days'])) {
            switch ($filters['expiry_days']) {
                case 'expired':
                    $where_conditions[] = 'ce.cn_expiry_date < CURDATE()';
                    break;
                case '0-10':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 10';
                    break;
                case '11-30':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 11 AND 30';
                    break;
                case '31-90':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 31 AND 90';
                    break;
                case '90+':
                    $where_conditions[] = 'DATEDIFF(ce.cn_expiry_date, CURDATE()) > 90';
                    break;
            }
        }

        if (!empty($filters['cn_series'])) {
            $where_conditions[] = '(CASE WHEN LEFT(ce.cn_number, 1) REGEXP "[0-9]" THEN LEFT(ce.cn_number, 2) ELSE LEFT(ce.cn_number, 1) END) = ?';
            $params[] = $filters['cn_series'];
        }

        if (!empty($filters['search'])) {
            // Search across all CNs (both open and used) regardless of usage filter
            // Search in CN number, allocated customer, and transaction customer
            $where_conditions[] = '(ce.cn_number LIKE ? OR ce.customer LIKE ? OR t.customer LIKE ?)';
            $search_term = '%' . $filters['search'] . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Get data
        $sql = "
            SELECT
                ce.cn_number,
                ce.cn_date as purchase_date,
                ce.cn_expiry_date,
                ce.cn_type,
                ce.username as purchased_by,
                ce.customer as allocated_customer,
                ce.allocation_date,
                DATEDIFF(ce.cn_expiry_date, CURDATE()) as days_until_expiry,
                CASE
                    WHEN ce.cn_expiry_date < CURDATE() THEN 'Expired'
                    WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) <= 10 THEN 'Expiring Soon'
                    ELSE 'Valid'
                END as expiry_status,
                CASE
                    WHEN LEFT(ce.cn_number, 1) REGEXP '[0-9]' THEN LEFT(ce.cn_number, 2)
                    ELSE LEFT(ce.cn_number, 1)
                END as cn_series,
                t.docket_date as used_date,
                t.created_at as transaction_created,
                t.customer as transaction_customer,
                CASE
                    WHEN t.docket_no IS NOT NULL THEN 'Used'
                    ELSE 'Open'
                END as usage_status
            FROM cn_entries ce
            LEFT JOIN transactions t ON ce.cn_number = t.docket_no
            $where_clause
            ORDER BY
                CASE
                    WHEN ce.cn_expiry_date < CURDATE() THEN 1
                    WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) <= 30 THEN 2
                    ELSE 3
                END,
                ce.cn_expiry_date ASC,
                ce.cn_number ASC
        ";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
        $stmt->execute();
        $data = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

        // Generate filename
        $filter_text = '';
        if (!empty($filters)) {
            $filter_parts = [];
            if (!empty($filters['usage']) && $filters['usage'] !== 'open') $filter_parts[] = 'Usage-' . $filters['usage'];
            if (!empty($filters['cn_series'])) $filter_parts[] = 'Series-' . $filters['cn_series'];
            if (!empty($filters['cn_type'])) $filter_parts[] = 'Type-' . $filters['cn_type'];
            if (!empty($filters['status'])) $filter_parts[] = 'Status-' . $filters['status'];
            if (!empty($filters['search'])) $filter_parts[] = 'Search-' . substr($filters['search'], 0, 10);

            if (!empty($filter_parts)) {
                $filter_text = '_' . implode('_', $filter_parts);
            }
        }

        $filename = 'Available_CN_Report_' . $username . $filter_text . '_' . date('Y-m-d_H-i-s') . '.csv';

        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        // Create output stream
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Add report header information
        fputcsv($output, ['Available CN Report']);
        fputcsv($output, ['Generated on: ' . date('d-m-Y H:i:s')]);
        fputcsv($output, ['User: ' . $username]);
        fputcsv($output, ['Total Records: ' . count($data)]);

        // Add filter information
        if (!empty($filters)) {
            fputcsv($output, ['Export Type: Filtered Data']);
            fputcsv($output, ['Applied Filters:']);
            if (!empty($filters['usage'])) fputcsv($output, ['', 'CN Usage: ' . ucfirst($filters['usage']) . ' CNs']);
            if (!empty($filters['cn_series'])) fputcsv($output, ['', 'CN Series: ' . $filters['cn_series']]);
            if (!empty($filters['cn_type'])) fputcsv($output, ['', 'CN Type: ' . $filters['cn_type']]);
            if (!empty($filters['status'])) fputcsv($output, ['', 'Status: ' . $filters['status']]);
            if (!empty($filters['search'])) fputcsv($output, ['', 'Search: ' . $filters['search']]);
        } else {
            $usage_type = ($filters['usage'] ?? 'open') === 'used' ? 'Used' : 'Open';
            fputcsv($output, ['Export Type: All ' . $usage_type . ' CNs']);
            fputcsv($output, ['Filters: None']);
        }

        fputcsv($output, []); // Empty row for separation

        // Add header row
        $headers = [
            'CN Number',
            'CN Series',
            'Purchase Date',
            'Expiry Date',
            'CN Type',
            'Purchased By',
            'Allocated Customer'
        ];

        // Add transaction customer column for used CNs
        if (($filters['usage'] ?? 'open') === 'used') {
            $headers[] = 'Transaction Customer';
        }

        $headers = array_merge($headers, [
            'Allocation Date',
            'Days Until Expiry',
            'Status',
            'Open/Used',
            'Used Date'
        ]);

        fputcsv($output, $headers);

        // Add data rows
        foreach ($data as $row) {
            // Determine the used date - prefer docket_date, fallback to transaction_created
            $used_date = '';
            if ($row['usage_status'] === 'Used') {
                if (!empty($row['used_date'])) {
                    $used_date = date('d-m-Y', strtotime($row['used_date']));
                } elseif (!empty($row['transaction_created'])) {
                    $used_date = date('d-m-Y', strtotime($row['transaction_created']));
                }
            }

            // Build row data
            $row_data = [
                $row['cn_number'],
                $row['cn_series'],
                $row['purchase_date'] ? date('d-m-Y', strtotime($row['purchase_date'])) : '-',
                $row['cn_expiry_date'] ? date('d-m-Y', strtotime($row['cn_expiry_date'])) : '-',
                $row['cn_type'],
                $row['purchased_by'],
                $row['allocated_customer'] ?: 'Unallocated'
            ];

            // Add transaction customer for used CNs
            if (($filters['usage'] ?? 'open') === 'used') {
                $row_data[] = $row['transaction_customer'] ?: '-';
            }

            // Add remaining fields
            $row_data = array_merge($row_data, [
                $row['allocation_date'] ? date('d-m-Y', strtotime($row['allocation_date'])) : '-',
                $row['days_until_expiry'] >= 0 ? $row['days_until_expiry'] . ' days' : 'Expired',
                $row['expiry_status'],
                $row['usage_status'],
                $used_date ?: '-'
            ]);

            fputcsv($output, $row_data);
        }

        fclose($output);

    } catch (Exception $e) {
        error_log("Excel export error: " . $e->getMessage());
        die("Error generating export: " . $e->getMessage());
    }
}

// End output buffering for normal page display
if (ob_get_level()) {
    ob_end_flush();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Available CN Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        :root {
            --primary-blue: #4361ee;
            --light-blue: #f1f4ff;
            --hover-blue: #3046c0;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #f8fafc;
            --success-green: #2ec4b6;
            --warning-orange: #ffc107;
            --danger-red: #ef476f;
            --card-shadow: 0 4px 20px rgba(0,0,0,0.05);
            --hover-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .report-container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .report-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            margin: 0 0 2rem 0;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
        }

        .report-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .report-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 0.5rem 1rem;
            box-shadow: var(--card-shadow);
            border-left: 4px solid;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            height: 10mm;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .summary-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--hover-shadow);
        }

        .summary-card.total { border-left-color: var(--primary-blue); }
        .summary-card.valid { border-left-color: var(--success-green); }
        .summary-card.expiring { border-left-color: var(--warning-orange); }
        .summary-card.expired { border-left-color: var(--danger-red); }

        .card-icon {
            font-size: 1.2rem;
            margin: 0;
            flex-shrink: 0;
        }

        .card-icon.total { color: var(--primary-blue); }
        .card-icon.valid { color: var(--success-green); }
        .card-icon.used { color: #6c757d; }
        .card-icon.expiring { color: var(--warning-orange); }
        .card-icon.expired { color: var(--danger-red); }

        .card-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 0;
        }

        .card-number {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            line-height: 1;
        }

        .card-label {
            font-size: 0.7rem;
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            margin: 0;
            line-height: 1;
        }

        .filters-section {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .filter-group label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
            outline: none;
        }

        .form-text {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: block;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-blue);
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-success {
            background-color: var(--success-green);
            color: white;
        }

        .btn-excel {
            background-color: #217346;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .data-table th {
            background: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
            padding: 1rem;
            text-align: left;
            border-bottom: 2px solid var(--border-color);
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .status-primary { background: #d1ecf1; color: #0c5460; }
        .status-info { background: #d1ecf1; color: #0c5460; }

        .pagination-container {
            background: white;
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .pagination {
            display: flex;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .pagination .page-link {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-dark);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .pagination .page-link:hover {
            background-color: var(--light-blue);
            border-color: var(--primary-blue);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
            color: white;
        }

        @media (max-width: 768px) {
            .report-header {
                padding: 1rem;
            }

            .report-title {
                font-size: 1.5rem;
            }

            .summary-cards {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .summary-card {
                height: 8mm;
                padding: 0.3rem 0.8rem;
                gap: 0.8rem;
            }

            .card-icon {
                font-size: 1rem;
            }

            .card-number {
                font-size: 1rem;
            }

            .card-label {
                font-size: 0.6rem;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .data-table {
                font-size: 0.8rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Header Section -->
        <div class="report-header">
            <h1 class="report-title">
                <i class="fas fa-clipboard-list"></i>
                Available CN Report
            </h1>
            <p class="report-subtitle">
                Track and manage available consignment numbers - User: <?php echo htmlspecialchars($username); ?>
            </p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #c3e6cb;">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['success_message']); ?>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger" style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #f5c6cb;">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($_SESSION['error_message']); ?>
            </div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card total">
                <div class="card-icon total">
                    <i class="fas fa-list"></i>
                </div>
                <div class="card-content">
                    <div class="card-number"><?php echo $summary_stats['success'] ? $summary_stats['data']['total_cns'] : '0'; ?></div>
                    <div class="card-label">Total CNs</div>
                </div>
            </div>
            <div class="summary-card valid">
                <div class="card-icon valid">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="card-content">
                    <div class="card-number"><?php echo $summary_stats['success'] ? $summary_stats['data']['total_available'] : '0'; ?></div>
                    <div class="card-label">Available CNs</div>
                </div>
            </div>
            <div class="summary-card used">
                <div class="card-icon used">
                    <i class="fas fa-check-square"></i>
                </div>
                <div class="card-content">
                    <div class="card-number"><?php echo $summary_stats['success'] ? $summary_stats['data']['total_used'] : '0'; ?></div>
                    <div class="card-label">Used CNs</div>
                </div>
            </div>
            <div class="summary-card expired">
                <div class="card-icon expired">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="card-content">
                    <div class="card-number"><?php echo $summary_stats['success'] ? $summary_stats['data']['expired'] : '0'; ?></div>
                    <div class="card-label">Expired CNs</div>
                </div>
            </div>
            <div class="summary-card expiring">
                <div class="card-icon expiring">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="card-content">
                    <div class="card-number"><?php echo $summary_stats['success'] ? $summary_stats['data']['expiring_soon'] : '0'; ?></div>
                    <div class="card-label">Expiring Soon (10 days)</div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <form method="GET" action="">
                <input type="hidden" name="page" value="available_cn_report">
                <div class="filters-row">
                    <div class="filter-group">
                        <label for="usageFilter">CN Usage</label>
                        <select class="form-select" id="usageFilter" name="usage">
                            <option value="open" <?php echo ($_GET['usage'] ?? 'open') === 'open' ? 'selected' : ''; ?>>Open CNs Only</option>
                            <option value="used" <?php echo ($_GET['usage'] ?? 'open') === 'used' ? 'selected' : ''; ?>>Used CNs Only</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="cnSeries">CN Series</label>
                        <select class="form-select" id="cnSeries" name="cn_series">
                            <option value="">All Series</option>
                            <?php if ($available_series['success']): ?>
                                <?php foreach ($available_series['data'] as $series): ?>
                                    <option value="<?php echo htmlspecialchars($series['series']); ?>"
                                            <?php echo ($_GET['cn_series'] ?? '') === $series['series'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($series['series']); ?> (<?php echo $series['count']; ?> CNs)
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="cnType">CN Type</label>
                        <select class="form-select" id="cnType" name="cn_type">
                            <option value="">All Types</option>
                            <option value="Physical" <?php echo ($_GET['cn_type'] ?? '') === 'Physical' ? 'selected' : ''; ?>>Physical</option>
                            <option value="Virtual" <?php echo ($_GET['cn_type'] ?? '') === 'Virtual' ? 'selected' : ''; ?>>Virtual</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="statusFilter">Status</label>
                        <select class="form-select" id="statusFilter" name="status">
                            <option value="">All Status</option>
                            <option value="valid" <?php echo ($_GET['status'] ?? '') === 'valid' ? 'selected' : ''; ?>>Valid</option>
                            <option value="expiring" <?php echo ($_GET['status'] ?? '') === 'expiring' ? 'selected' : ''; ?>>Expiring Soon</option>
                            <option value="expired" <?php echo ($_GET['status'] ?? '') === 'expired' ? 'selected' : ''; ?>>Expired</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="expiryDaysFilter">Expiry Days</label>
                        <select class="form-select" id="expiryDaysFilter" name="expiry_days">
                            <option value="">All Days</option>
                            <option value="expired" <?php echo ($_GET['expiry_days'] ?? '') === 'expired' ? 'selected' : ''; ?>>Expired</option>
                            <option value="0-10" <?php echo ($_GET['expiry_days'] ?? '') === '0-10' ? 'selected' : ''; ?>>0-10 Days</option>
                            <option value="11-30" <?php echo ($_GET['expiry_days'] ?? '') === '11-30' ? 'selected' : ''; ?>>11-30 Days</option>
                            <option value="31-90" <?php echo ($_GET['expiry_days'] ?? '') === '31-90' ? 'selected' : ''; ?>>31-90 Days</option>
                            <option value="90+" <?php echo ($_GET['expiry_days'] ?? '') === '90+' ? 'selected' : ''; ?>>90+ Days</option>
                        </select>
                    </div>
                </div>

                <div class="filters-row">
                    <div class="filter-group">
                        <label for="searchBox">Search CN Number / Customer</label>
                        <input type="text" class="form-control" id="searchBox" name="search"
                               placeholder="Search CN number, allocated customer, or transaction customer..."
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                        <small class="form-text text-muted">When searching, shows all matching CNs (both open and used). Clear search to use Open/Used filter.</small>
                    </div>
                </div>


                <div class="filters-row">
                    <div class="filter-group">
                        <label>&nbsp;</label>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>Apply Filters
                            </button>
                            <a href="?page=available_cn_report" class="btn btn-secondary">
                                <i class="fas fa-times"></i>Clear
                            </a>
                            <a href="?page=available_cn_report<?php echo !empty($_GET) ? '&' . http_build_query(array_filter($_GET, function($k) { return $k !== 'page'; }, ARRAY_FILTER_USE_KEY)) : ''; ?>" class="btn btn-success">
                                <i class="fas fa-sync-alt"></i>Refresh
                            </a>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>Bulk Actions</label>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <button type="button" id="selectAllBtn" class="btn btn-secondary">
                                <i class="fas fa-check-square"></i>Select All
                            </button>
                            <button type="button" id="deleteSelectedBtn" class="btn" style="background-color: #dc3545; color: white;" disabled>
                                <i class="fas fa-trash"></i>Delete Selected
                            </button>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>Export Data</label>
                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            <?php
                            $current_filters = array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'cn_page'; }, ARRAY_FILTER_USE_KEY);
                            $has_filters = !empty($current_filters);
                            $export_url = '?page=available_cn_report&export=excel' . ($has_filters ? '&' . http_build_query($current_filters) : '');

                            $usage_type = ($_GET['usage'] ?? 'open') === 'used' ? 'Used' : 'Open';
                            if ($has_filters) {
                                $button_text = 'Download Filtered Data';
                                $button_title = 'Download data matching current filters';
                            } else {
                                $button_text = 'Download All ' . $usage_type . ' CNs';
                                $button_title = 'Download all ' . strtolower($usage_type) . ' CNs';
                            }
                            ?>
                            <a href="<?php echo $export_url; ?>" class="btn btn-excel" title="<?php echo $button_title; ?>">
                                <i class="fas fa-file-excel"></i><?php echo $button_text; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Main Data Table -->
        <div class="table-container">
            <!-- Delete Form -->
            <form id="deleteForm" method="POST" style="display: none;">
                <input type="hidden" name="delete_cns" value="1">
                <div id="selectedCNsContainer"></div>
            </form>

            <table class="data-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <input type="checkbox" id="selectAllCheckbox" title="Select All">
                        </th>
                        <th>CN Number</th>
                        <th>Purchase Date</th>
                        <th>Expiry Date</th>
                        <th>Type</th>
                        <th>Allocated Customer</th>
                        <?php if (($_GET['usage'] ?? 'open') === 'used'): ?>
                        <th>Transaction Customer</th>
                        <?php endif; ?>
                        <th>Allocation Date</th>
                        <th>Days Left</th>
                        <th>Status</th>
                        <th>Open/Used</th>
                        <th>Used Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($table_data['success'] && !empty($table_data['data'])): ?>
                        <?php foreach ($table_data['data'] as $row): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="cn-checkbox" value="<?php echo htmlspecialchars($row['cn_number']); ?>" name="selected_cns[]">
                                </td>
                                <td><strong><?php echo htmlspecialchars($row['cn_number']); ?></strong></td>
                                <td><?php echo formatDate($row['purchase_date']); ?></td>
                                <td><?php echo formatDate($row['cn_expiry_date']); ?></td>
                                <td><?php echo getTypeBadge($row['cn_type']); ?></td>
                                <td><?php echo htmlspecialchars($row['allocated_customer'] ?? 'Unallocated'); ?></td>
                                <?php if (($_GET['usage'] ?? 'open') === 'used'): ?>
                                <td><?php echo htmlspecialchars($row['transaction_customer'] ?? '-'); ?></td>
                                <?php endif; ?>
                                <td><?php echo formatDate($row['allocation_date']); ?></td>
                                <td><?php echo $row['days_until_expiry'] >= 0 ? $row['days_until_expiry'] . ' days' : 'Expired'; ?></td>
                                <td><?php echo getStatusBadge($row['expiry_status']); ?></td>
                                <td><?php echo getUsageBadge($row['usage_status']); ?></td>
                                <td><?php echo formatUsedDate($row); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?php echo (($_GET['usage'] ?? 'open') === 'used') ? '12' : '11'; ?>" style="text-align: center; padding: 2rem;">
                                <?php if (!$table_data['success']): ?>
                                    <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                                    Error: <?php echo htmlspecialchars($table_data['error'] ?? 'Failed to load data'); ?>
                                <?php else: ?>
                                    <i class="fas fa-inbox"></i>
                                    No CNs found matching your criteria
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- Pagination Footer -->
            <?php if ($table_data['success'] && $table_data['pagination']['total_records'] > 0): ?>
            <div class="pagination-container">
                <div class="pagination-info">
                    Showing <?php echo $table_data['pagination']['start_record']; ?> to <?php echo $table_data['pagination']['end_record']; ?> of <?php echo $table_data['pagination']['total_records']; ?> entries
                </div>
                <ul class="pagination">
                    <?php
                    $pagination = $table_data['pagination'];
                    $current_params = $_GET;

                    // Previous button
                    if ($pagination['current_page'] > 1):
                        $current_params['cn_page'] = $pagination['current_page'] - 1;
                    ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query($current_params); ?>">Previous</a>
                        </li>
                    <?php endif; ?>

                    <?php
                    // Page numbers
                    $start_page = max(1, $pagination['current_page'] - 2);
                    $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);

                    for ($i = $start_page; $i <= $end_page; $i++):
                        $current_params['cn_page'] = $i;
                        $active_class = $i === $pagination['current_page'] ? 'active' : '';
                    ?>
                        <li class="page-item <?php echo $active_class; ?>">
                            <a class="page-link" href="?<?php echo http_build_query($current_params); ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php
                    // Next button
                    if ($pagination['current_page'] < $pagination['total_pages']):
                        $current_params['cn_page'] = $pagination['current_page'] + 1;
                    ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query($current_params); ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Download button loading state
            const downloadButton = document.querySelector('.btn-excel');
            if (downloadButton) {
                downloadButton.addEventListener('click', function() {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Generating...';
                    this.style.pointerEvents = 'none';

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.style.pointerEvents = 'auto';
                    }, 3000);
                });
            }

            // Multiselect functionality
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const cnCheckboxes = document.querySelectorAll('.cn-checkbox');
            const selectAllBtn = document.getElementById('selectAllBtn');
            const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
            const deleteForm = document.getElementById('deleteForm');
            const selectedCNsContainer = document.getElementById('selectedCNsContainer');

            // Select All checkbox functionality
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    cnCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateDeleteButtonState();
                });
            }

            // Individual checkbox change
            cnCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectAllState();
                    updateDeleteButtonState();
                });
            });

            // Select All button
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    const allChecked = Array.from(cnCheckboxes).every(cb => cb.checked);
                    cnCheckboxes.forEach(checkbox => {
                        checkbox.checked = !allChecked;
                    });
                    updateSelectAllState();
                    updateDeleteButtonState();
                });
            }

            // Delete Selected button
            if (deleteSelectedBtn) {
                deleteSelectedBtn.addEventListener('click', function() {
                    const selectedCNs = Array.from(cnCheckboxes)
                        .filter(cb => cb.checked)
                        .map(cb => cb.value);

                    if (selectedCNs.length === 0) {
                        alert('Please select at least one CN to delete.');
                        return;
                    }

                    const confirmMessage = `Are you sure you want to delete ${selectedCNs.length} selected CN(s)?\n\nThis action cannot be undone.`;
                    if (confirm(confirmMessage)) {
                        // Clear existing hidden inputs
                        selectedCNsContainer.innerHTML = '';

                        // Add selected CNs as hidden inputs
                        selectedCNs.forEach(cnNumber => {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'selected_cns[]';
                            input.value = cnNumber;
                            selectedCNsContainer.appendChild(input);
                        });

                        // Submit the form
                        deleteForm.submit();
                    }
                });
            }

            function updateSelectAllState() {
                if (selectAllCheckbox) {
                    const checkedCount = Array.from(cnCheckboxes).filter(cb => cb.checked).length;
                    selectAllCheckbox.checked = checkedCount === cnCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < cnCheckboxes.length;
                }
            }

            function updateDeleteButtonState() {
                const selectedCount = Array.from(cnCheckboxes).filter(cb => cb.checked).length;
                if (deleteSelectedBtn) {
                    deleteSelectedBtn.disabled = selectedCount === 0;
                    deleteSelectedBtn.innerHTML = selectedCount > 0
                        ? `<i class="fas fa-trash"></i>Delete Selected (${selectedCount})`
                        : '<i class="fas fa-trash"></i>Delete Selected';
                }
                if (selectAllBtn) {
                    const allChecked = Array.from(cnCheckboxes).every(cb => cb.checked);
                    selectAllBtn.innerHTML = allChecked
                        ? '<i class="fas fa-square"></i>Deselect All'
                        : '<i class="fas fa-check-square"></i>Select All';
                }
            }

            // Initialize states
            updateSelectAllState();
            updateDeleteButtonState();
        });
    </script>

</body>
</html>
