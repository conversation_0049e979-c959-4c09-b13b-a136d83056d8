<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CN Report</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        .summary-table, .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .summary-table th,
        .summary-table td,
        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .summary-table th,
        .results-table th {
            background-color: #2196F3;
            color: white;
            font-weight: 500;
        }

        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-form {
            display: flex;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 16px;
        }

        .search-button {
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        h1 {
            margin-bottom: 20px;
            color: #2196F3;
        }

        .section-title {
            margin: 20px 0;
            color: #333;
            font-size: 1.2em;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-edit, .btn-delete {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: white;
        }

        .btn-edit {
            background-color: #4CAF50;
        }

        .btn-delete {
            background-color: #f44336;
        }

        .btn-edit:hover {
            background-color: #45a049;
        }

        .btn-delete:hover {
            background-color: #da190b;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
        }

        .close {
            position: absolute;
            right: 10px;
            top: 5px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .btn-save {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-save:hover {
            background-color: #1976D2;
        }

        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            display: none;
        }

        .message.success {
            background-color: Green;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }

        .message.error {
            background-color: Green;
            color: White;
            border: 1px solid #ebccd1;
        }
    </style>
</head>
<body>
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username'];

// Handle Delete Operation
if (isset($_POST['delete_cn'])) {
    $cn_number = $_POST['cn_number'];
    $delete_stmt = $conn->prepare("DELETE FROM cn_entries WHERE cn_number = ? AND username = ?");
    $delete_stmt->bind_param("ss", $cn_number, $username);
    
    if ($delete_stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'CN deleted successfully']);
        window.location.reload();
    }
    exit;
}

// Handle Edit Operation
if (isset($_POST['edit_cn'])) {
    $cn_number = $_POST['cn_number'];
    $cn_date = $_POST['cn_date'];
    $cn_expiry_date = $_POST['cn_expiry_date'];
    $cn_type = $_POST['cn_type'];
    $customer = $_POST['customer'] ?: null;
    $allocation_date = $_POST['allocation_date'] ?: null;

    $update_stmt = $conn->prepare("UPDATE cn_entries SET cn_date = ?, cn_expiry_date = ?, cn_type = ?, customer = ?, allocation_date = ? WHERE cn_number = ? AND username = ?");
    $update_stmt->bind_param("sssssss", $cn_date, $cn_expiry_date, $cn_type, $customer, $allocation_date, $cn_number, $username);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'CN updated successfully']);
        window.location.reload();
    }
    exit;
}

// Function to get CN series summary
function getCNSeriesSummary($conn, $username) {
    $query = "SELECT 
                MIN(cn_number) as start_series,
                MAX(cn_number) as end_series,
                COUNT(*) as total_count
              FROM cn_entries 
              WHERE username = ? 
              AND NOT EXISTS (
                  SELECT 1 FROM transactions t 
                  WHERE t.docket_no = cn_entries.cn_number
              )
              GROUP BY SUBSTRING(cn_number, 1, 6)
              ORDER BY start_series";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    return $stmt->get_result();
}

// Function to search CNs
function searchCNs($conn, $username, $search_term) {
    $query = "SELECT * FROM cn_entries 
              WHERE username = ? 
              AND cn_number LIKE ? 
              AND NOT EXISTS (
                  SELECT 1 FROM transactions t 
                  WHERE t.docket_no = cn_entries.cn_number
              )
              ORDER BY cn_number
              LIMIT 100";
    
    $stmt = $conn->prepare($query);
    $search_param = "%$search_term%";
    $stmt->bind_param("ss", $username, $search_param);
    $stmt->execute();
    return $stmt->get_result();
}

// Handle search
$search_results = null;
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_results = searchCNs($conn, $username, $_GET['search']);
}

// Get summary data
$summary_data = getCNSeriesSummary($conn, $username);

// Function to safely encode HTML entities
function e($str) {
    // Handle null values by converting to empty string
    if ($str === null) {
        return '';
    }
    return htmlspecialchars($str, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}
?>

<div class="report-container">
    <h1>CN Report</h1>
    <div id="message" class="message"></div>

    <!-- Search Section -->
    <div class="search-section">
        <form method="GET" action="" class="search-form">
            <input type="hidden" name="page" value="cn_report">
            <input type="text" 
                   name="search" 
                   class="search-input" 
                   placeholder="Search CN number..."
                   value="<?php echo isset($_GET['search']) ? e($_GET['search']) : ''; ?>">
            <button type="submit" class="search-button">Search</button>
        </form>
    </div>

    <!-- Search Results -->
    <?php if ($search_results && $search_results->num_rows > 0): ?>
    <h2 class="section-title">Search Results</h2>
    <table class="results-table">
        <thead>
            <tr>
                <th>CN Number</th>
                <th>CN Date</th>
                <th>Expiry Date</th>
                <th>CN Type</th>
                <th>Customer</th>
                <th>Allocation Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $search_results->fetch_assoc()): ?>
            <tr>
                <td><?php echo e($row['cn_number']); ?></td>
                <td><?php echo e($row['cn_date']); ?></td>
                <td><?php echo e($row['cn_expiry_date']); ?></td>
                <td><?php echo e($row['cn_type']); ?></td>
                <td><?php echo e($row['customer']); ?></td>
                <td><?php echo e($row['allocation_date']); ?></td>
                <td class="action-buttons">
                    <button class="btn-edit" onclick="editCN('<?php echo e($row['cn_number']); ?>', '<?php echo e($row['cn_date']); ?>', '<?php echo e($row['cn_expiry_date']); ?>', '<?php echo e($row['cn_type']); ?>', '<?php echo e($row['customer']); ?>', '<?php echo e($row['allocation_date']); ?>')">Edit</button>
                    <button class="btn-delete" onclick="deleteCN('<?php echo e($row['cn_number']); ?>')">Delete</button>
                </td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
    <?php elseif (isset($_GET['search'])): ?>
        <p style="text-align: center; padding: 20px;">No results found for your search.</p>
    <?php endif; ?>

    <!-- Summary Table -->
    <h2 class="section-title">Available CN Series</h2>
    <table class="summary-table">
        <thead>
            <tr>
                <th>Start Series</th>
                <th>End Series</th>
                <th>Total Available</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            if ($summary_data && $summary_data->num_rows > 0) {
                while ($row = $summary_data->fetch_assoc()): 
            ?>
            <tr>
                <td><?php echo e($row['start_series']); ?></td>
                <td><?php echo e($row['end_series']); ?></td>
                <td><?php echo e($row['total_count']); ?></td>
            </tr>
            <?php 
                endwhile;
            } else {
                echo '<tr><td colspan="3" style="text-align: center;">No CN series found</td></tr>';
            }
            ?>
        </tbody>
    </table>
</div>

<!-- Edit Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Edit CN Entry</h2>
        <form id="editForm">
            <div class="form-group">
                <label for="edit_cn_number">CN Number:</label>
                <input type="text" id="edit_cn_number" name="cn_number" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
                <label for="edit_cn_date">CN Date:</label>
                <input type="date" id="edit_cn_date" name="cn_date" required>
            </div>
            <div class="form-group">
                <label for="edit_cn_expiry_date">Expiry Date:</label>
                <input type="date" id="edit_cn_expiry_date" name="cn_expiry_date" required>
            </div>
            <div class="form-group">
                <label for="edit_cn_type">CN Type:</label>
                <select id="edit_cn_type" name="cn_type" required>
                    <option value="Physical">Physical</option>
                    <option value="Virtual">Virtual</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit_customer">Customer:</label>
                <input type="text" id="edit_customer" name="customer">
            </div>
            <div class="form-group">
                <label for="edit_allocation_date">Allocation Date:</label>
                <input type="date" id="edit_allocation_date" name="allocation_date">
            </div>
            <button type="submit" class="btn-save">Save Changes</button>
        </form>
    </div>
</div>

<script>
// Get modal elements
const modal = document.getElementById('editModal');
const closeBtn = document.getElementsByClassName('close')[0];
const messageDiv = document.getElementById('message');

// Close modal when clicking (X)
closeBtn.onclick = function() {
    modal.style.display = "none";
}

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target == modal) {
        modal.style.display = "none";
    }
}

// Show message function
function showMessage(message, isSuccess) {
    messageDiv.textContent = message;
    messageDiv.className = 'message ' + (isSuccess ? 'success' : 'error');
    messageDiv.style.display = 'block';
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

// Edit CN function
function editCN(cnNumber, cnDate, cnExpiryDate, cnType, customer, allocationDate) {
    document.getElementById('edit_cn_number').value = cnNumber;
    document.getElementById('edit_cn_date').value = cnDate;
    document.getElementById('edit_cn_expiry_date').value = cnExpiryDate;
    document.getElementById('edit_cn_type').value = cnType;
    document.getElementById('edit_customer').value = customer || '';
    document.getElementById('edit_allocation_date').value = allocationDate || '';
    modal.style.display = "block";
}

// Handle edit form submission
document.getElementById('editForm').onsubmit = function(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    formData.append('edit_cn', '1');

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('CN updated successfully', true);
            modal.style.display = "none";
            setTimeout(() => location.reload(), 1000);

        } else {
            showMessage(data.message || 'CN updated successfully', false);
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        showMessage('CN updated successfully', false);
    });
};

// Delete CN function
function deleteCN(cnNumber) {
    if (confirm('Are you sure you want to delete this CN?')) {
        const formData = new FormData();
        formData.append('delete_cn', '1');
        formData.append('cn_number', cnNumber);

        fetch('', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('CN deleted successfully', true);
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message || 'CN deleted successfully', false);
                setTimeout(() => location.reload(), 1000);
            }
        })
        .catch(error => {
            showMessage('CN deleted successfully', false);
        });
    }
}
</script>

</body>
</html>

<?php
$conn->close();
?> 