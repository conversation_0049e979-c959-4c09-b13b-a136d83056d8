<?php

require_once 'db_connect.php';



if (!isset($_SESSION['username'])) {

    header('Location: login.php');

    exit();

}



$username = $_SESSION['username'];



// Fetch settings data with error handling
try {
    $stmt = $conn->prepare("SELECT * FROM settings WHERE username = ?");
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("s", $username);
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $settings = $result->fetch_assoc();
    
    // If no settings found, initialize with default values
    if (!$settings) {
        $settings = [
            'express_cn_cost' => '0.00',
            'aircargo_cn_cost' => '0.00',
            'surface_cn_cost' => '0.00',
            'premium_cn_cost' => '0.00',
            'region_series' => '',
            'fsc' => '0.00',
            'gst' => '0.00',
            'metro_status' => 'Non-Metro',
            'billing_zone' => '',
            'invoice_series' => '',
            'invoice_number' => '0001',
            'invoice_temp' => 'Regular',
            'surface_docket' => '',
            'aircargo_docket' => '',
            'premium_docket' => '',
            'ptp_docket' => '',
            'cod_docket' => '',
            'international_docket' => '',
            'ecom_express_docket' => '',
            'ecom_surface_docket' => ''
        ];
    }
} catch (Exception $e) {
    error_log("Error fetching settings: " . $e->getMessage());
    $_SESSION['settings_message'] = "Error loading settings: " . $e->getMessage();
    $settings = [];
}



// Get success message if exists

$success_message = '';

if (isset($_SESSION['settings_message'])) {

    $success_message = $_SESSION['settings_message'];

    unset($_SESSION['settings_message']);

}

?>



<!DOCTYPE html>

<html lang="en">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Settings</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>

        .settings-container {

            max-width: 800px;

            margin: 20px auto;

            padding: 20px;

            background: white;

            border-radius: 10px;

            box-shadow: 0 0 10px rgba(0,0,0,0.1);

        }

        .form-grid {

            display: grid;

            grid-template-columns: repeat(3, 1fr);

            gap: 20px;

        }

        .form-group {

            margin-bottom: 15px;

        }

        .form-group label {

            display: block;

            margin-bottom: 5px;

            font-weight: bold;

            color: #333;

        }

        .form-group input, .form-group select {

            width: 100%;

            padding: 8px;

            border: 1px solid #ddd;

            border-radius: 4px;

            box-sizing: border-box;

        }

        .form-group input:focus, .form-group select:focus {

            border-color: #4CAF50;

            outline: none;

        }

        .button-group {

            grid-column: 1 / -1;

            display: flex;

            gap: 10px;

            justify-content: flex-end;

        }

        .submit-btn, .reset-btn {

            padding: 10px 20px;

            border: none;

            border-radius: 4px;

            cursor: pointer;

            font-size: 16px;

        }

        .submit-btn {

            background: #4CAF50;

            color: white;

        }

        .reset-btn {

            background: #f44336;

            color: white;

        }

        .submit-btn:hover {

            background: #45a049;

        }

        .reset-btn:hover {

            background: #d32f2f;

        }

        .section-title {

            grid-column: 1 / -1;

            margin-top: 20px;

            padding-bottom: 10px;

            border-bottom: 2px solid #4CAF50;

            color: #333;

        }

        .message {

            position: fixed;

            top: 20px;

            right: 20px;

            padding: 15px 25px;

            border-radius: 4px;

            background-color: #4CAF50;

            color: white;

            font-size: 16px;

            z-index: 1000;

            display: none;

            animation: slideIn 0.5s ease-out;

        }

        @keyframes slideIn {

            from {

                transform: translateX(100%);

                opacity: 0;

            }

            to {

                transform: translateX(0);

                opacity: 1;

            }

        }

        .show-message {

            display: block;

        }

    </style>

</head>

<body>



<?php if ($success_message): ?>

    <div class="message" id="successMessage">

        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>

    </div>

    <script>

        document.addEventListener('DOMContentLoaded', function() {

            const message = document.getElementById('successMessage');

            message.classList.add('show-message');

            setTimeout(() => {

                message.classList.remove('show-message');

            }, 3000);

        });

    </script>

<?php endif; ?>



<div class="settings-container">

    <h2><i class="fas fa-cog"></i> Settings</h2>



    <form action="index.php?page=update_settings" method="POST" id="settingsForm">

        <div class="form-grid">

            <h3 class="section-title">Update C-Note cost (price of single c-note)</h3>

            

            <div class="form-group">

                <label for="express_cn_cost">Express/Regional</label>

                <input type="number" step="0.01" id="express_cn_cost" name="express_cn_cost" 

                    value="<?php echo htmlspecialchars($settings['express_cn_cost'] ?? '0.00'); ?>">

            </div>



            <div class="form-group">

                <label for="aircargo_cn_cost">Air Cargo</label>

                <input type="number" step="0.01" id="aircargo_cn_cost" name="aircargo_cn_cost" 

                    value="<?php echo htmlspecialchars($settings['aircargo_cn_cost'] ?? '0.00'); ?>">

            </div>



            <div class="form-group">

                <label for="surface_cn_cost">Surface</label>

                <input type="number" step="0.01" id="surface_cn_cost" name="surface_cn_cost" 

                    value="<?php echo htmlspecialchars($settings['surface_cn_cost'] ?? '0.00'); ?>">

            </div>



            <div class="form-group">

                <label for="premium_cn_cost">Premium</label>

                <input type="number" step="0.01" id="premium_cn_cost" name="premium_cn_cost" 

                    value="<?php echo htmlspecialchars($settings['premium_cn_cost'] ?? '0.00'); ?>">

            </div>



            <h3 class="section-title">MISC Details</h3>



            <div class="form-group">

                <label for="invoice_series">Invoice Series Text</label>

                <input type="text" id="invoice_series" name="invoice_series" 

                    placeholder="Like ABC/2023-24/" 

                    value="<?php echo htmlspecialchars($settings['invoice_series'] ?? ''); ?>"

                    title="Format: ABC/2023-24/">

                <small class="form-text text-muted">This will be used as prefix for all your invoices</small>

            </div>

            <div class="form-group">

                <label for="invoice_number">Invoice Number</label>

                <input type="text" id="invoice_number" name="invoice_number" 

                    placeholder="0001" 

                    value="<?php echo htmlspecialchars($settings['invoice_number'] ?? '0001'); ?>"

                    title="Starting invoice number">

                <small class="form-text text-muted">This will be your starting invoice number</small>

            </div>

            <div class="form-group">

                <label for="manual_series">Manual Series Text</label>

                <input type="text" id="manual_series" name="manual_series" 

                    placeholder="Like MAN/2023-24/" 

                    value="<?php echo htmlspecialchars($settings['manual_series'] ?? ''); ?>"

                    title="Format: MAN/2023-24/">

                <small class="form-text text-muted">This will be used as prefix for all your manual entries</small>

            </div>



            <div class="form-group">

                <label for="manual_invoice_number">Manual Invoice Number</label>

                <input type="text" id="manual_invoice_number" name="manual_invoice_number" 

                    placeholder="0001" 

                    value="<?php echo htmlspecialchars($settings['manual_invoice_number'] ?? '0001'); ?>"

                    title="Starting manual invoice number">

                <small class="form-text text-muted">This will be your starting manual invoice number</small>

            </div>



            



            <div class="form-group">

                <label for="invoice_temp">Invoice Template</label>

                <select id="invoice_temp" name="invoice_temp">

                    <option value="Regular" <?php echo ($settings['invoice_temp'] ?? '') === 'Regular' ? 'selected' : ''; ?>>Regular</option>

                    <option value="Template-1" <?php echo ($settings['invoice_temp'] ?? '') === 'Template-1' ? 'selected' : ''; ?>>Template-1</option>
                    <option value="Template-2" <?php echo ($settings['invoice_temp'] ?? '') === 'Template-2' ? 'selected' : ''; ?>>Template-2</option>

                </select>  
            </div>



            <div class="form-group">

                <label for="fsc">Fuel Surcharge</label>

                <input type="number" step="0.01" id="fsc" name="fsc" 

                    value="<?php echo htmlspecialchars($settings['fsc'] ?? '0.00'); ?>">

            </div>



            <div class="form-group">

                <label for="gst">GST %</label>

                <input type="number" step="0.01" id="gst" name="gst" 

                    value="<?php echo htmlspecialchars($settings['gst'] ?? '0.00'); ?>">

            </div>



            <div class="form-group">

                <label for="metro_status">Metro Status</label>

                <select id="metro_status" name="metro_status">

                    <option value="Metro" <?php echo ($settings['metro_status'] ?? '') === 'Metro' ? 'selected' : ''; ?>>Metro</option>

                    <option value="Non-Metro" <?php echo ($settings['metro_status'] ?? '') === 'Non-Metro' ? 'selected' : ''; ?>>Non-Metro</option>

                </select>

            </div>



            <div class="form-group">

                <label for="billing_zone">Billing Zone</label>

                <input type="text" id="billing_zone" name="billing_zone" 

                    value="<?php echo htmlspecialchars($settings['billing_zone'] ?? ''); ?>">

            </div>



            <h3 class="section-title">Docket Series</h3>

            <div class="form-group">

                <label for="region_series">Regional Series</label>

                <input type="text" id="region_series" name="region_series" 

                    value="<?php echo htmlspecialchars($settings['region_series'] ?? ''); ?>">

            </div>


            <div class="form-group">

                <label for="surface_docket">Surface</label>

                <input type="text" id="surface_docket" name="surface_docket" 

                    value="<?php echo htmlspecialchars($settings['surface_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="aircargo_docket">Air Cargo</label>

                <input type="text" id="aircargo_docket" name="aircargo_docket" 

                    value="<?php echo htmlspecialchars($settings['aircargo_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="premium_docket">Premium</label>

                <input type="text" id="premium_docket" name="premium_docket" 

                    value="<?php echo htmlspecialchars($settings['premium_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="ptp_docket">PTP</label>

                <input type="text" id="ptp_docket" name="ptp_docket" 

                    value="<?php echo htmlspecialchars($settings['ptp_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="cod_docket">COD</label>

                <input type="text" id="cod_docket" name="cod_docket" 

                    value="<?php echo htmlspecialchars($settings['cod_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="international_docket">International</label>

                <input type="text" id="international_docket" name="international_docket" 

                    value="<?php echo htmlspecialchars($settings['international_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="ecom_express_docket">E-Com Express</label>

                <input type="text" id="ecom_express_docket" name="ecom_express_docket" 

                    value="<?php echo htmlspecialchars($settings['ecom_express_docket'] ?? ''); ?>">

            </div>



            <div class="form-group">

                <label for="ecom_surface_docket">E-Com Surface</label>

                <input type="text" id="ecom_surface_docket" name="ecom_surface_docket" 

                    value="<?php echo htmlspecialchars($settings['ecom_surface_docket'] ?? ''); ?>">

            </div>



            <div class="button-group">

                <button type="button" class="reset-btn" onclick="resetForm()">Reset</button>

                <button type="submit" class="submit-btn">Save Changes</button>

            </div>

        </div>

    </form>

</div>



<script>

function resetForm() {

    document.getElementById('settingsForm').reset();

}

</script>



</body>

</html> 