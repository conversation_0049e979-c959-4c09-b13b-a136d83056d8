<?php
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    die("You must be logged in!");
}

$username = $_SESSION['username'];

echo "<h2>Database Connection Test</h2>";
echo "<p>Username: " . htmlspecialchars($username) . "</p>";

// Test 1: Check if cn_entries table exists and has data
echo "<h3>Test 1: CN Entries Table</h3>";
try {
    $sql = "SELECT COUNT(*) as total FROM cn_entries WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    echo "<p>Total CN entries for user: " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Show sample data
        $sql = "SELECT cn_number, cn_date, cn_expiry_date, cn_type FROM cn_entries WHERE username = ? LIMIT 5";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "<table border='1'>";
        echo "<tr><th>CN Number</th><th>CN Date</th><th>Expiry Date</th><th>Type</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['cn_number']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_date']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_expiry_date']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_type']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: Check transactions table
echo "<h3>Test 2: Transactions Table</h3>";
try {
    $sql = "SELECT COUNT(*) as total FROM transactions WHERE username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    echo "<p>Total transactions for user: " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Show sample data
        $sql = "SELECT docket_no, docket_date FROM transactions WHERE username = ? AND docket_no IS NOT NULL LIMIT 5";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "<table border='1'>";
        echo "<tr><th>Docket No</th><th>Docket Date</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['docket_no']) . "</td>";
            echo "<td>" . htmlspecialchars($row['docket_date']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 3: Available CNs query
echo "<h3>Test 3: Available CNs Query</h3>";
try {
    $sql = "
        SELECT 
            COUNT(*) as total_available,
            COUNT(CASE WHEN ce.cn_expiry_date < CURDATE() THEN 1 END) as expired,
            COUNT(CASE WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) BETWEEN 0 AND 30 THEN 1 END) as expiring_soon,
            COUNT(CASE WHEN DATEDIFF(ce.cn_expiry_date, CURDATE()) > 30 THEN 1 END) as valid
        FROM cn_entries ce
        WHERE ce.username = ?
        AND NOT EXISTS (
            SELECT 1 FROM transactions t
            WHERE t.docket_no = ce.cn_number AND t.username = ?
        )
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $username, $username);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    echo "<p>Available CNs Summary:</p>";
    echo "<ul>";
    echo "<li>Total Available: " . $result['total_available'] . "</li>";
    echo "<li>Valid: " . $result['valid'] . "</li>";
    echo "<li>Expiring Soon: " . $result['expiring_soon'] . "</li>";
    echo "<li>Expired: " . $result['expired'] . "</li>";
    echo "</ul>";
    
    if ($result['total_available'] > 0) {
        // Show sample available CNs
        $sql = "
            SELECT ce.cn_number, ce.cn_date, ce.cn_expiry_date, ce.cn_type,
                   DATEDIFF(ce.cn_expiry_date, CURDATE()) as days_until_expiry
            FROM cn_entries ce
            WHERE ce.username = ?
            AND NOT EXISTS (
                SELECT 1 FROM transactions t
                WHERE t.docket_no = ce.cn_number AND t.username = ?
            )
            LIMIT 10
        ";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $username, $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "<h4>Sample Available CNs:</h4>";
        echo "<table border='1'>";
        echo "<tr><th>CN Number</th><th>CN Date</th><th>Expiry Date</th><th>Type</th><th>Days Left</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['cn_number']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_date']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_expiry_date']) . "</td>";
            echo "<td>" . htmlspecialchars($row['cn_type']) . "</td>";
            echo "<td>" . $row['days_until_expiry'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<br><br><a href='?page=available_cn_report'>Back to Available CN Report</a>";
?>
