<?php 
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if session is not already started before starting it
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'db_connect.php'; // Database connection

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username'];
$id = $_POST['id'] ?? '';
$short_name = trim($_POST['short_name']);
$p_p = trim($_POST['p_p']);
$address1 = trim($_POST['address1']);
$address2 = trim($_POST['address2']);
$address3 = trim($_POST['address3']);
$gst_no = trim($_POST['gst_no']);
$gst_apl = trim($_POST['gst_apl']);
$fsc_percent = trim($_POST['fsc_percent']);
$waybill_percent = trim($_POST['waybill_percent']);
$owner_risk = trim($_POST['owner_risk']);
$carrier_risk = trim($_POST['carrier_risk']);

try {
    // Prevent Duplicate short_name for the same user
    $check_stmt = $conn->prepare("SELECT id FROM customers WHERE short_name = ? AND username = ? AND id != ?");
    $check_stmt->bind_param("ssi", $short_name, $username, $id);
    $check_stmt->execute();
    $check_stmt->store_result();

    if ($check_stmt->num_rows > 0) {
        die("<script>alert('Error: Short Name already exists for your account!'); window.history.back();</script>");
    }
    $check_stmt->close();

    // **Update Mode**
    if (!empty($id)) {
        $stmt = $conn->prepare("UPDATE customers SET short_name=?, p_p=?, address1=?, address2=?, address3=?, gst_no=?, gst_apl=?, fsc_percent=?, waybill_percent=?, owner_risk=?, carrier_risk=? WHERE id=? AND username=?");
        $stmt->bind_param("sssssssdddisi", $short_name, $p_p, $address1, $address2, $address3, $gst_no, $gst_apl, $fsc_percent, $waybill_percent, $owner_risk, $carrier_risk, $id, $username);
    } 
    // **Insert Mode**
    else {
        $stmt = $conn->prepare("INSERT INTO customers (short_name, p_p, address1, address2, address3, gst_no, gst_apl, fsc_percent, waybill_percent, owner_risk, carrier_risk, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssssssdddds", $short_name, $p_p, $address1, $address2, $address3, $gst_no, $gst_apl, $fsc_percent, $waybill_percent, $owner_risk, $carrier_risk, $username);
    }

    // Execute & Redirect
    if ($stmt->execute()) {
        echo "<script>alert('✅ Customer successfully saved!'); window.location.href = 'index.php?page=customer_dashboard';</script>";
    } else {
        throw new Exception("Could not save customer.");
    }

    $stmt->close();

} catch (mysqli_sql_exception $e) {
    // Handle specific MySQL errors
    if ($e->getCode() == 1062) { // Duplicate entry error
        die("<script>alert('Error: This short name is already used by another user. Please choose a different short name.'); window.history.back();</script>");
    } else {
        die("<script>alert('Database Error: " . htmlspecialchars($e->getMessage()) . "'); window.history.back();</script>");
    }
} catch (Exception $e) {
    die("<script>alert('Error: " . htmlspecialchars($e->getMessage()) . "'); window.history.back();</script>");
} finally {
    $conn->close();
}
?>
