<?php
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: ../login.php");
    exit();
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['excel_file'])) {
    require '../vendor/autoload.php';

    try {
        $username = $_SESSION['username'];
        $file = $_FILES['excel_file'];

        // Validate file
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!in_array($file['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Please upload an Excel file (.xlsx or .xls)');
        }

        // Read Excel file
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        // Remove header row
        array_shift($rows);

        // Prepare insert statement
        $stmt = $conn->prepare("INSERT INTO booking_data (DSR_CNNO, DSR_BOOKING_DATE, DSR_DEST_PINCODE, DSR_CN_WEIGHT, DSR_MODE, DSR_CN_TYPE, username, created_at, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)");

        // Prepare statement to check docket_no, pincode, weight and mode_of_tsp
        $check_stmt = $conn->prepare("SELECT docket_no, pincode, weight, mode_of_tsp FROM transactions WHERE docket_no = ? AND username = ?");

        $successCount = 0;
        $errors = [];

        foreach ($rows as $index => $row) {
            if (empty($row[0])) continue; // Skip empty rows

            try {
                // Validate and format data
                $dsr_cnno = trim($row[0]);
                $booking_date = date('Y-m-d', strtotime(trim($row[1])));
                $dest_pincode = trim($row[2]);
                $dsr_cn_weight = floatval(trim($row[3]));
                $dsr_mode = trim($row[4]);
                $dsr_cn_type = trim($row[5]);

                // Basic validation
                if (!preg_match('/^\d{6}$/', $dest_pincode)) {
                    throw new Exception("Invalid pincode format");
                }

                // Check if docket_no exists in transactions table and validate pincode, weight and mode
                $check_stmt->bind_param("ss", $dsr_cnno, $username);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $check_row = $check_result->fetch_assoc();

                $remarks = [];
                if (!$check_row) {
                    $remarks[] = 'Docket entry missing';
                } else {
                    if ($check_row['pincode'] != $dest_pincode) {
                        $remarks[] = 'Destination pincode mismatch';
                    }
                    if ($check_row['weight'] != $dsr_cn_weight) {
                        if ($check_row['weight'] < $dsr_cn_weight) {
                            $remarks[] = 'Negative weight';
                        } else {
                            $remarks[] = 'Positive weight';
                        }
                    }
                    // Check mode validation for all modes
                    if ($check_row['mode_of_tsp'] == 'Premium' && in_array($dsr_cn_type, ['AR1', 'AC1', 'SF1'])) {
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Express' && $dsr_cn_type != 'AR1') {
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Surface' && $dsr_cn_type != 'SF1') {
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Air Cargo' && $dsr_cn_type != 'AC1') {
                        $remarks[] = 'Mode mismatch';
                    }
                }

                // Join remarks with comma if multiple exist
                $remarks_text = implode(', ', $remarks);

                // Bind parameters and execute
                $stmt->bind_param("sssdssss",
                    $dsr_cnno,
                    $booking_date,
                    $dest_pincode,
                    $dsr_cn_weight,
                    $dsr_mode,
                    $dsr_cn_type,
                    $username,
                    $remarks_text
                );

                if ($stmt->execute()) {
                    $successCount++;
                } else {
                    $errors[] = "Error on row " . ($index + 2) . ": " . $stmt->error;
                }
            } catch (Exception $e) {
                $errors[] = "Error on row " . ($index + 2) . ": " . $e->getMessage();
            }
        }

        if ($successCount > 0) {
            $_SESSION['upload_message'] = "Successfully imported $successCount records!";
            if (!empty($errors)) {
                $_SESSION['upload_errors'] = $errors;
            }
        } else {
            throw new Exception("No records were imported. Please check your file format.");
        }

    } catch (Exception $e) {
        $_SESSION['upload_error'] = $e->getMessage();
    }

    header("Location: upload.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Booking Data (Compact Layout)</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/fontawesome.min.css">
    <link rel="stylesheet" href="../css/all.min.css">
</head>
<body>
    <div class="container booking-container">
        <?php if (isset($_SESSION['upload_message'])): ?>
            <div class="message success" id="successMessage">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_message']); ?>
            </div>
            <?php unset($_SESSION['upload_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_error'])): ?>
            <div class="message error" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_error']); ?>
            </div>
            <?php unset($_SESSION['upload_error']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_errors'])): ?>
            <div class="message warning" id="warningMessage">
                <i class="fas fa-exclamation-triangle"></i> Some rows had errors:
                <ul>
                    <?php foreach ($_SESSION['upload_errors'] as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php unset($_SESSION['upload_errors']); ?>
        <?php endif; ?>

        <div class="header-section">
            <h1 class="page-title">📊 Upload Booking Data (Compact Layout)</h1>
            <form method="POST" enctype="multipart/form-data" class="upload-section">
                <div class="file-upload-container">
                    <div class="file-input-wrapper">
                        <input type="file"
                               name="excel_file"
                               class="file-input"
                               accept=".xlsx,.xls"
                               required>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload"></i>
                    Submit
                </button>
                <a href="../upload/Booking_data_upload_template.xlsx" class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    Download Excel Template
                </a>
            </form>
        </div>
    </div>
</body>
</html> 