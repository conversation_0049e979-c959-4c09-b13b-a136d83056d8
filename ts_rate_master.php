<?php 
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];
$editMode = false;
$rate = [
    'id' => '',
    'mode_of_tsp' => '',
    'zone' => '',
    'up_to_0100' => '',
    'up_to_0250' => '',
    'up_to_0500' => '',
    'addl_500gm' => '',
    'up_to_5kg' => '',
    'up_to_10kg' => '',
    'up_to_25kg' => '',
    'up_to_50kg' => '',
    'above_50kg' => '',
    'min_weight' => ''
];

if (isset($_GET['id'])) {
    $editMode = true;
    $id = $_GET['id'];
    $stmt = $conn->prepare("SELECT * FROM ts_rate_master WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $id, $username);
    $stmt->execute();
    $result = $stmt->get_result();
    $rate = $result->fetch_assoc();
}

$sql = "SELECT * FROM ts_rate_master WHERE username = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TS Rate Master</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            font-family: Arial, sans-serif;
            background: var(--background);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        h2, h3 {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        form {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .button-group {
            grid-column: 1 / -1;
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        select, input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
            height: 42px;
            box-sizing: border-box;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        button[type="submit"] {
            background: var(--primary-blue);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 150px;
        }

        button[type="submit"]:hover {
            background: var(--hover-blue);
            transform: translateY(-1px);
        }

        button[type="submit"]:active {
            transform: translateY(0);
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: var(--background);
        }

        button {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            margin: 0 0.25rem;
        }

        button[onclick*="editRate"] {
            background: var(--light-blue);
            color: var(--primary-blue);
        }

        button[onclick*="editRate"]:hover {
            background: var(--primary-blue);
            color: white;
        }

        button[onclick*="deleteRate"] {
            background: #FEE2E2;
            color: #DC2626;
        }

        button[onclick*="deleteRate"]:hover {
            background: #DC2626;
            color: white;
        }

        /* Success Message Styles */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .message.success {
            background-color: #4CAF50;
        }

        .message.error {
            background-color: #DC2626;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }

        @media (max-width: 1024px) {
            .container {
                padding: 1rem;
            }

            form {
                padding: 1.5rem;
            }

            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            td, th {
                min-width: 120px;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .button-group {
                justify-content: center;
            }
        }

        .weight-rate {
            transition: all 0.3s ease;
        }
        
        .weight-rate.express-premium {
            display: block;
        }

        /* Global Filter Section Styles */
        .global-filter-section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .global-filter-section .filter-group {
            display: flex;
            gap: 15px;
            flex: 1;
        }

        .global-filter-section .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 300px;
            background-color: white;
            cursor: pointer;
        }

        .global-filter-section .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .global-filter-section .filter-button {
            background: var(--primary-blue);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .global-filter-section .filter-button:hover {
            background: var(--hover-blue);
        }

        .global-filter-section .reset-button {
            background: #f3f4f6;
            color: #4b5563;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .global-filter-section .reset-button:hover {
            background: #e5e7eb;
        }

        @media (max-width: 768px) {
            .global-filter-section {
                flex-direction: column;
                align-items: stretch;
                position: static;
            }
            
            .global-filter-section .filter-group {
                flex-direction: column;
            }
            
            .global-filter-section .filter-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>

<?php if (isset($_SESSION['rate_message'])): ?>
    <?php 
    $messageType = (strpos(strtolower($_SESSION['rate_message']), 'successfully') !== false) ? 'success' : 'error';
    ?>
    <div class="message <?php echo $messageType; ?>" id="successMessage">
        <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i> 
        <?php echo htmlspecialchars($_SESSION['rate_message']); ?>
    </div>
    <?php unset($_SESSION['rate_message']); ?>
<?php endif; ?>

<div class="container">
    <h2><?php echo $editMode ? 'Edit Rate' : 'Add New Rate'; ?></h2>

    <form action="index.php?page=process_ts_rate" method="POST">
        <input type="hidden" name="id" value="<?php echo $rate['id']; ?>">
        
        <div class="form-grid">
            <div class="form-group">
                <label>Mode:</label>
                <select name="mode_of_tsp" id="mode_of_tsp" required onchange="handleModeChange()">
                    <option value="">Select Mode</option>
                    <option value="Express" <?php echo ($rate['mode_of_tsp'] == 'Express' || !$editMode) ? 'selected' : ''; ?>>Express</option>
                    <option value="Surface" <?php echo ($rate['mode_of_tsp'] == 'Surface') ? 'selected' : ''; ?>>Surface</option>
                    <option value="Premium" <?php echo ($rate['mode_of_tsp'] == 'Premium') ? 'selected' : ''; ?>>Premium</option>
                    <option value="Air Cargo" <?php echo ($rate['mode_of_tsp'] == 'Air Cargo') ? 'selected' : ''; ?>>Air Cargo</option>
                </select>
            </div>

            <div class="form-group">
                <label>Zone:</label>
                <select name="zone" required>
                    <option value="">Select Zone</option>
                    <?php 
                    $zones = ['Local', 'Pune', 'Maharashtra', 'South GJ', 'West Zone', 'Metro', 'ROI', 'HJKG', 'North East'];
                    foreach ($zones as $zone) {
                        $selected = ($rate['zone'] == $zone) ? "selected" : "";
                        echo "<option value='$zone' $selected>$zone</option>";
                    }
                    ?>
                </select>
            </div>

            <!-- Express Mode Fields -->
            <div class="form-group weight-rate express-mode">
                <label>Up to 100gms:</label>
                <input type="number" step="0.01" name="up_to_0100" value="<?php echo $rate['up_to_0100']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate express-mode">
                <label>Up to 250gms:</label>
                <input type="number" step="0.01" name="up_to_0250" value="<?php echo $rate['up_to_0250']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate express-mode">
                <label>Up to 500gms:</label>
                <input type="number" step="0.01" name="up_to_0500" value="<?php echo $rate['up_to_0500']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate express-mode">
                <label>Additional 500gms:</label>
                <input type="number" step="0.01" name="addl_500gm" value="<?php echo $rate['addl_500gm']; ?>" placeholder="Enter rate">
            </div>

            <!-- Surface Mode Fields -->
            <div class="form-group weight-rate surface-mode">
                <label>MCW (Minimum Chargeable Weight):</label>
                <input type="number" step="0.01" name="min_weight" value="<?php echo $rate['min_weight']; ?>" placeholder="Enter MCW">
            </div>

            <!-- Weight Fields for Surface Only -->
            <div class="form-group weight-rate surface-mode">
                <label>Up to 5kg:</label>
                <input type="number" step="0.01" name="up_to_5kg" value="<?php echo $rate['up_to_5kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate surface-mode">
                <label>Up to 10kg:</label>
                <input type="number" step="0.01" name="up_to_10kg" value="<?php echo $rate['up_to_10kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate surface-mode">
                <label>Up to 25kg:</label>
                <input type="number" step="0.01" name="up_to_25kg" value="<?php echo $rate['up_to_25kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate surface-mode">
                <label>Up to 50kg:</label>
                <input type="number" step="0.01" name="up_to_50kg" value="<?php echo $rate['up_to_50kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate surface-mode">
                <label>Above 50kg:</label>
                <input type="number" step="0.01" name="above_50kg" value="<?php echo $rate['above_50kg']; ?>" placeholder="Enter rate">
            </div>

            <!-- Premium Mode Fields -->
            <div class="form-group weight-rate premium-mode">
                <label>Up to 250gms:</label>
                <input type="number" step="0.01" name="up_to_0250" value="<?php echo $rate['up_to_0250']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate premium-mode">
                <label>Up to 500gms:</label>
                <input type="number" step="0.01" name="up_to_0500" value="<?php echo $rate['up_to_0500']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate premium-mode">
                <label>Additional 500gms:</label>
                <input type="number" step="0.01" name="addl_500gm" value="<?php echo $rate['addl_500gm']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate premium-mode">
                <label>Above 10kg:</label>
                <input type="number" step="0.01" name="above_50kg" value="<?php echo $rate['above_50kg']; ?>" placeholder="Enter rate">
            </div>

            <!-- Air Cargo Mode Fields (same as Surface) -->
            <div class="form-group weight-rate air-cargo-mode">
                <label>MCW (Minimum Chargeable Weight):</label>
                <input type="number" step="0.01" name="min_weight" value="<?php echo $rate['min_weight']; ?>" placeholder="Enter MCW">
            </div>

            <div class="form-group weight-rate air-cargo-mode">
                <label>Up to 5kg:</label>
                <input type="number" step="0.01" name="up_to_5kg" value="<?php echo $rate['up_to_5kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate air-cargo-mode">
                <label>Up to 10kg:</label>
                <input type="number" step="0.01" name="up_to_10kg" value="<?php echo $rate['up_to_10kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate air-cargo-mode">
                <label>Up to 25kg:</label>
                <input type="number" step="0.01" name="up_to_25kg" value="<?php echo $rate['up_to_25kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate air-cargo-mode">
                <label>Up to 50kg:</label>
                <input type="number" step="0.01" name="up_to_50kg" value="<?php echo $rate['up_to_50kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate air-cargo-mode">
                <label>Above 50kg:</label>
                <input type="number" step="0.01" name="above_50kg" value="<?php echo $rate['above_50kg']; ?>" placeholder="Enter rate">
            </div>

            <div class="button-group">
                <button type="submit">
                    <?php echo $editMode ? "Update Rate" : "Save Rate"; ?>
                </button>
            </div>
        </div>
    </form>

    <hr>

    <div class="global-filter-section">
        <div class="filter-group">
            <select id="globalZoneFilter" class="filter-input">
                <option value="">All Zones</option>
                <?php
                $zone_sql = "SELECT DISTINCT zone FROM ts_rate_master WHERE username = ? ORDER BY zone";
                $zone_stmt = $conn->prepare($zone_sql);
                $zone_stmt->bind_param("s", $username);
                $zone_stmt->execute();
                $zone_result = $zone_stmt->get_result();
                while ($row = $zone_result->fetch_assoc()) {
                    echo "<option value='" . $row['zone'] . "'>" . $row['zone'] . "</option>";
                }
                ?>
            </select>
        </div>
        <button onclick="filterAllTables()" class="filter-button">Apply Filter</button>
        <button onclick="resetAllFilters()" class="reset-button">Reset</button>
    </div>

    <!-- Express Rate List -->
    <h3>Express Rate List</h3>
    <table id="express-table">
        <thead>
            <tr>
                <th>Zone</th>
                <th>Up to 100g</th>
                <th>Up to 250g</th>
                <th>Up to 500g</th>
                <th>Addl 500g</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Express') {
        ?>
            <tr>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['up_to_0100']; ?></td>
                <td><?php echo $row['up_to_0250']; ?></td>
                <td><?php echo $row['up_to_0500']; ?></td>
                <td><?php echo $row['addl_500gm']; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>

    <!-- Surface Rate List -->
    <h3 style="margin-top: 2rem;">Surface Rate List</h3>
    <table id="surface-table">
        <thead>
            <tr>
                <th>Zone</th>
                <th>MCW</th>
                <th>Up to 5kg</th>
                <th>Up to 10kg</th>
                <th>Up to 25kg</th>
                <th>Up to 50kg</th>
                <th>Above 50kg</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Surface') {
        ?>
            <tr>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['min_weight']; ?></td>
                <td><?php echo $row['up_to_5kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_10kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_25kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_50kg'] ?? '0'; ?></td>
                <td><?php echo $row['above_50kg'] ?? '0'; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>

    <!-- Air Cargo Rate List -->
    <h3 style="margin-top: 2rem;">Air Cargo Rate List</h3>
    <table id="air-cargo-table">
        <thead>
            <tr>
                <th>Zone</th>
                <th>MCW</th>
                <th>Up to 5kg</th>
                <th>Up to 10kg</th>
                <th>Up to 25kg</th>
                <th>Up to 50kg</th>
                <th>Above 50kg</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Air Cargo') {
        ?>
            <tr>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['min_weight']; ?></td>
                <td><?php echo $row['up_to_5kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_10kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_25kg'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_50kg'] ?? '0'; ?></td>
                <td><?php echo $row['above_50kg'] ?? '0'; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>

    <!-- Premium Rate List -->
    <h3 style="margin-top: 2rem;">Premium Rate List</h3>
    <table id="premium-table">
        <thead>
            <tr>
                <th>Zone</th>
                <th>Up to 250g</th>
                <th>Up to 500g</th>
                <th>Addl 500g</th>
                <th>Above 10kg</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Premium') {
        ?>
            <tr>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['up_to_0250'] ?? '0'; ?></td>
                <td><?php echo $row['up_to_0500'] ?? '0'; ?></td>
                <td><?php echo $row['addl_500gm'] ?? '0'; ?></td>
                <td><?php echo $row['above_50kg'] ?? '0'; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const successMessage = document.getElementById('successMessage');
    if (successMessage) {
        successMessage.classList.add('show-message');
        setTimeout(() => {
            successMessage.classList.remove('show-message');
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 500);
        }, 3000);
    }

    // Initialize form based on selected mode
    handleModeChange();
});

function handleModeChange() {
    const mode = document.getElementById('mode_of_tsp').value;
    const expressFields = document.querySelectorAll('.weight-rate.express-mode');
    const surfaceFields = document.querySelectorAll('.weight-rate.surface-mode');
    const premiumFields = document.querySelectorAll('.weight-rate.premium-mode');
    const airCargoFields = document.querySelectorAll('.weight-rate.air-cargo-mode');
    const isEditMode = document.querySelector('input[name="id"]').value !== '';

    // Hide all fields first
    document.querySelectorAll('.weight-rate').forEach(field => {
        field.style.display = 'none';
        const input = field.querySelector('input');
        if (input) {
            input.required = false;
            input.disabled = true;
            // Only clear values if not in edit mode
            if (!isEditMode) {
                input.value = '';
            }
        }
    });

    // Show relevant fields based on mode
    if (mode === 'Express') {
        expressFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
    } else if (mode === 'Surface') {
        surfaceFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
    } else if (mode === 'Premium') {
        premiumFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
    } else if (mode === 'Air Cargo') {
        airCargoFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
    }
}

function editRate(id) {
    window.location.href = 'index.php?page=ts_rate_master&id=' + id;
}

function deleteRate(id) {
    if (confirm("Are you sure you want to delete this rate?")) {
        window.location.href = 'index.php?page=delete_ts_rate&id=' + id;
    }
}

function filterAllTables() {
    const zoneFilter = document.getElementById('globalZoneFilter').value;
    const tables = ['express-table', 'surface-table', 'air-cargo-table', 'premium-table'];
    
    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');
        
        // Start from index 1 to skip header row
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const zone = row.cells[0].textContent;
            
            // If no zone is selected, show all rows
            if (!zoneFilter) {
                row.style.display = '';
            } else {
                row.style.display = zone === zoneFilter ? '' : 'none';
            }
        }
    });
}

function resetAllFilters() {
    const tables = ['express-table', 'surface-table', 'air-cargo-table', 'premium-table'];
    
    // Reset filter select
    document.getElementById('globalZoneFilter').value = '';
    
    // Show all rows in all tables
    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            rows[i].style.display = '';
        }
    });
}

// Add event listener for global filter
document.addEventListener('DOMContentLoaded', function() {
    const zoneFilter = document.getElementById('globalZoneFilter');
    if (zoneFilter) {
        zoneFilter.addEventListener('change', filterAllTables);
    }
});
</script>

</body>
</html>  