<?php
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// For debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Require the PhpSpreadsheet library
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

try {
    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Set column header
    $sheet->setCellValue('A1', 'Docket No.');
    
    // Style the header
    $sheet->getStyle('A1')->applyFromArray([
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => ['rgb' => '2196F3']
        ]
    ]);

    // Set column width
    $sheet->getColumnDimension('A')->setWidth(20);

    // Add sample data in row 2
    $sheet->setCellValue('A2', 'A12345678');

    // Get dockets with status 'Open'
    $username = $_SESSION['username'];
    
    // Debug: Print username
    error_log("Username: " . $username);
    
    // Clear any previous output
    ob_clean();
    
    $query = "SELECT tr_docket_no FROM tracking WHERE username = ? AND tr_opcl = 'Open' ORDER BY tr_docket_dt DESC";
    $stmt = $conn->prepare($query);
    
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        throw new Exception("Database prepare failed");
    }
    
    $stmt->bind_param("s", $username);
    $success = $stmt->execute();
    
    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        throw new Exception("Query execution failed");
    }
    
    $result = $stmt->get_result();
    
    // Debug: Print number of rows
    error_log("Number of rows found: " . $result->num_rows);

    // Add docket numbers starting from row 3
    $row = 3;
    while ($data = $result->fetch_assoc()) {
        error_log("Adding docket: " . $data['tr_docket_no']);
        $sheet->setCellValue('A' . $row, $data['tr_docket_no']);
        $row++;
    }

    // Style the entire column
    $lastRow = $sheet->getHighestRow();
    error_log("Last row in sheet: " . $lastRow);
    
    $sheet->getStyle('A1:A' . $lastRow)->applyFromArray([
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
            ]
        ]
    ]);

    // Clear any previous output and set headers
    if (ob_get_length()) ob_end_clean();
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="open_dockets_list.xlsx"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');

    // Create Excel file and output it
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
} catch (Exception $e) {
    error_log("Error in download_open_dockets.php: " . $e->getMessage());
    echo "Error creating Excel file: " . $e->getMessage();
} finally {
    // Close connections
    if (isset($stmt)) $stmt->close();
    if (isset($conn)) $conn->close();
} 