<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start output buffering to catch any errors
ob_start();

// Only start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fix the database connection path
$db_path = __DIR__ . '/../db_connect.php';
if (!file_exists($db_path)) {
    die("Database connection file not found at: " . $db_path);
}
include $db_path;

if (!isset($_SESSION['username'])) {
    header("Location: ../login.php");
    exit();
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['excel_file'])) {
    $debug_output = [];
    $debug_output[] = "File upload process started";
    
    try {
        // Fix the vendor autoload path
        $vendor_path = __DIR__ . '/../vendor/autoload.php';
        if (!file_exists($vendor_path)) {
            throw new Exception('Composer autoload file not found at: ' . $vendor_path);
        }
        
        require $vendor_path;
        $debug_output[] = "Autoload file included successfully";

        $username = $_SESSION['username'];
        $file = $_FILES['excel_file'];
        $debug_output[] = "File details: " . print_r($file, true);

        // Check if file upload was successful
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
                UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
                UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
            ];
            $error_message = isset($upload_errors[$file['error']]) ? $upload_errors[$file['error']] : 'Unknown upload error';
            throw new Exception('File upload error: ' . $error_message);
        }

        // Validate file
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        $debug_output[] = "File type: " . $file['type'];
        if (!in_array($file['type'], $allowed_types)) {
            $debug_output[] = "Invalid file type detected: " . $file['type'];
            throw new Exception('Invalid file type. Please upload an Excel file (.xlsx or .xls)');
        }

        // Read Excel file
        $debug_output[] = "Attempting to load Excel file";
        try {
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            $debug_output[] = "Excel file loaded successfully. Number of rows: " . count($rows);
        } catch (Exception $e) {
            throw new Exception('Error reading Excel file: ' . $e->getMessage());
        }

        // Remove header row
        array_shift($rows);

        // Prepare insert statement
        $stmt = $conn->prepare("INSERT INTO booking_data (DSR_CNNO, DSR_BOOKING_DATE, DSR_DEST_PINCODE, DSR_CN_WEIGHT, DSR_MODE, DSR_CN_TYPE, username, created_at, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)");

        // Prepare statement to check docket_no, pincode, weight and mode_of_tsp
        $check_stmt = $conn->prepare("SELECT docket_no, pincode, weight, mode_of_tsp FROM transactions WHERE docket_no = ? AND username = ?");

        // Prepare statement to check for duplicate DSR_CNNO in booking_data
        $duplicate_check_stmt = $conn->prepare("SELECT DSR_CNNO FROM booking_data WHERE DSR_CNNO = ? AND username = ?");

        // Prepare statement to check if docket exists in transactions
        $docket_check_stmt = $conn->prepare("SELECT docket_no FROM transactions WHERE docket_no = ? AND username = ?");

        $successCount = 0;
        $errors = [];
        $duplicates = [];

        foreach ($rows as $index => $row) {
            if (empty($row[1])) {
                $debug_output[] = "Skipping empty row at index: " . $index;
                continue;
            }

            try {
                $debug_output[] = "\n=== Processing Row " . ($index + 2) . " ===";
                $debug_output[] = "Raw row data: " . print_r($row, true);
                
                // New column mapping
                $dsr_cnno = trim($row[1]); // B
                $dsr_cn_weight = floatval(trim($row[4])); // E
                $dsr_cn_type = trim($row[5]); // F
                $dsr_mode = trim($row[7]); // H
                $dest_pincode = trim($row[9]); // J
                $booking_date = date('Y-m-d', strtotime(trim($row[10]))); // K

                $debug_output[] = "Processed values:";
                $debug_output[] = "- DSR_CNNO: $dsr_cnno";
                $debug_output[] = "- Weight: $dsr_cn_weight";
                $debug_output[] = "- Type: $dsr_cn_type";
                $debug_output[] = "- Mode: $dsr_mode";
                $debug_output[] = "- Pincode: $dest_pincode";
                $debug_output[] = "- Booking Date: $booking_date";

                // Use the logged-in user's username instead of the one from Excel
                $username = $_SESSION['username'];
                $debug_output[] = "Using username: $username";

                // Initialize remarks array
                $remarks = [];

                // First check if docket exists in transactions table
                $docket_check_stmt->bind_param("ss", $dsr_cnno, $username);
                $docket_check_stmt->execute();
                $docket_result = $docket_check_stmt->get_result();
                
                if ($docket_result->num_rows === 0) {
                    $debug_output[] = "WARNING: Docket not found in transactions table";
                    $remarks[] = 'Docket entry missing';
                } else {
                    $debug_output[] = "Docket found in transactions table";
                }

                // Check for duplicate DSR_CNNO in booking_data
                $duplicate_check_stmt->bind_param("ss", $dsr_cnno, $username);
                $duplicate_check_stmt->execute();
                $duplicate_result = $duplicate_check_stmt->get_result();
                
                $debug_output[] = "Duplicate check details:";
                $debug_output[] = "- Checking DSR_CNNO: '$dsr_cnno'";
                $debug_output[] = "- Username: '$username'";
                $debug_output[] = "- SQL Query: SELECT DSR_CNNO FROM booking_data WHERE DSR_CNNO = '$dsr_cnno' AND username = '$username'";
                $debug_output[] = "- Found matches: " . $duplicate_result->num_rows;
                
                if ($duplicate_result->num_rows > 0) {
                    $debug_output[] = "ERROR: Duplicate docket found in booking_data";
                    $duplicates[] = "Row " . ($index + 2) . ": Docket number '$dsr_cnno' already exists in booking data";
                    continue;
                } else {
                    $debug_output[] = "No duplicate found in booking_data";
                }

                // Basic validation - only check if pincode is numeric
                if (!is_numeric($dest_pincode)) {
                    $debug_output[] = "WARNING: Invalid pincode format: $dest_pincode";
                    $remarks[] = "Invalid pincode format";
                }

                // Check if docket_no exists in transactions table and validate pincode, weight and mode
                $check_stmt->bind_param("ss", $dsr_cnno, $username);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $check_row = $check_result->fetch_assoc();

                if ($check_row) {
                    $debug_output[] = "Validating against transactions data:";
                    $debug_output[] = "- Transaction pincode: " . $check_row['pincode'];
                    $debug_output[] = "- Transaction weight: " . $check_row['weight'];
                    $debug_output[] = "- Transaction mode: " . $check_row['mode_of_tsp'];

                    if ($check_row['pincode'] != $dest_pincode) {
                        $debug_output[] = "WARNING: Pincode mismatch";
                        $remarks[] = 'Destination pincode mismatch';
                    }
                    if ($check_row['weight'] != $dsr_cn_weight) {
                        if ($check_row['weight'] < $dsr_cn_weight) {
                            $debug_output[] = "WARNING: Negative weight difference";
                            $remarks[] = 'Negative weight';
                        } else {
                            $debug_output[] = "WARNING: Positive weight difference";
                            $remarks[] = 'Positive weight';
                        }
                    }
                    // Check mode validation for all modes
                    if ($check_row['mode_of_tsp'] == 'Premium' && in_array($dsr_cn_type, ['AR1', 'AC1', 'SF1'])) {
                        $debug_output[] = "WARNING: Mode mismatch for Premium";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Express' && $dsr_cn_type != 'AR1') {
                        $debug_output[] = "WARNING: Mode mismatch for Express";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Surface' && $dsr_cn_type != 'SF1') {
                        $debug_output[] = "WARNING: Mode mismatch for Surface";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Air Cargo' && $dsr_cn_type != 'AC1') {
                        $debug_output[] = "WARNING: Mode mismatch for Air Cargo";
                        $remarks[] = 'Mode mismatch';
                    }
                }

                // Join remarks with comma if multiple exist
                $remarks_text = implode(', ', $remarks);
                $debug_output[] = "Final remarks: " . ($remarks_text ?: 'None');

                // Bind parameters and execute
                $stmt->bind_param("sssdssss",
                    $dsr_cnno,
                    $booking_date,
                    $dest_pincode,
                    $dsr_cn_weight,
                    $dsr_mode,
                    $dsr_cn_type,
                    $username,
                    $remarks_text
                );

                if ($stmt->execute()) {
                    $successCount++;
                    $debug_output[] = "SUCCESS: Row inserted successfully";
                } else {
                    $debug_output[] = "ERROR: Failed to insert row - " . $stmt->error;
                    $errors[] = "Error on row " . ($index + 2) . ": " . $stmt->error;
                }
            } catch (Exception $e) {
                $debug_output[] = "CRITICAL ERROR in row " . ($index + 2) . ": " . $e->getMessage();
                $errors[] = "Error on row " . ($index + 2) . ": " . $e->getMessage();
            }
        }

        $debug_output[] = "\n=== Import Summary ===";
        $debug_output[] = "Total rows processed: " . count($rows);
        $debug_output[] = "Successfully imported: " . $successCount;
        $debug_output[] = "Errors encountered: " . count($errors);
        $debug_output[] = "Duplicates found: " . count($duplicates);

        if ($successCount > 0) {
            $_SESSION['upload_message'] = "Successfully imported $successCount records!";
            if (!empty($errors)) {
                $_SESSION['upload_errors'] = $errors;
            }
            if (!empty($duplicates)) {
                $_SESSION['upload_duplicates'] = $duplicates;
            }
        } else {
            if (!empty($duplicates)) {
                // If all records were duplicates, show a more specific message
                $_SESSION['upload_error'] = "All records were duplicates. Please check if these records have already been imported.";
            } else {
                $_SESSION['upload_error'] = "No records were imported. Please check your file format.";
            }
        }

        // Store summary information in session
        $_SESSION['upload_summary'] = [
            'total_rows' => count($rows),
            'success_count' => $successCount,
            'error_count' => count($errors),
            'duplicate_count' => count($duplicates)
        ];

    } catch (Exception $e) {
        $debug_output[] = "Critical error in upload process: " . $e->getMessage();
        $_SESSION['upload_error'] = $e->getMessage();
    }

    // Store debug output in session
    $_SESSION['debug_output'] = $debug_output;
    
    // Get any output that might have been generated
    $output = ob_get_clean();
    if (!empty($output)) {
        $_SESSION['debug_output'][] = "Additional output: " . $output;
    }
    
    // Fix the redirect URL to use the correct query parameter structure
    header("Location: ../index.php?page=upload");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Booking Data (Compact Layout)</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/fontawesome.min.css">
    <link rel="stylesheet" href="../css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }
        .booking-container {
            padding: 1rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            background: linear-gradient(45deg, #f8f9fa, #ffffff);
        }
        .header-section {
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            margin-bottom: 1rem;
            border-left: 3px solid var(--primary-blue);
        }
        .page-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
        }
        .upload-section {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        .file-upload-container {
            flex: 1;
            min-width: 280px;
        }
        .file-input-wrapper {
            position: relative;
            width: 100%;
        }
        .file-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }
        .btn-primary:hover {
            background: var(--hover-blue);
        }
        .btn-secondary {
            background: var(--light-blue);
            color: var(--primary-blue);
        }
        .btn-secondary:hover {
            background: var(--primary-blue);
            color: white;
        }
        @media (max-width: 768px) {
            .booking-container {
                padding: 0.75rem;
            }
            .header-section {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }
            .upload-section {
                flex-direction: column;
            }
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-section h3 {
            color: #495057;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container booking-container">
        <div class="message warning" style="background: #f8d7da; color: #a94442; border: 1px solid #f5c6cb; padding: 1rem 1.5rem; border-radius: 8px; margin-bottom: 1.5rem; font-size: 1.15rem; display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; color: #a94442;"></i>
            <strong>Page Under Construction:</strong> This page is currently being updated. Some features may not work as expected.
        </div>
        <?php if (isset($_SESSION['upload_message'])): ?>
            <div class="message success" id="successMessage">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_message']); ?>
            </div>
            <?php unset($_SESSION['upload_message']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_error'])): ?>
            <div class="message error" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_error']); ?>
            </div>
            <?php unset($_SESSION['upload_error']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_errors'])): ?>
            <div class="message warning" id="warningMessage">
                <i class="fas fa-exclamation-triangle"></i> Some rows had errors:
                <ul>
                    <?php foreach ($_SESSION['upload_errors'] as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php unset($_SESSION['upload_errors']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_duplicates'])): ?>
            <div class="message warning" id="warningMessage">
                <i class="fas fa-exclamation-triangle"></i> Some rows were duplicates:
                <ul>
                    <?php foreach ($_SESSION['upload_duplicates'] as $duplicate): ?>
                        <li><?php echo htmlspecialchars($duplicate); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php unset($_SESSION['upload_duplicates']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['upload_summary'])): ?>
            <div class="summary-section" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin: 1rem 0;">
                <h3 style="color: #495057; margin-top: 0; margin-bottom: 1rem;">Upload Summary</h3>
                <div class="summary-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                    <div class="stat-box" style="background: white; padding: 1rem; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div style="font-size: 0.9rem; color: #6c757d;">Total Rows Processed</div>
                        <div style="font-size: 1.5rem; font-weight: 600; color: #495057;"><?php echo $_SESSION['upload_summary']['total_rows']; ?></div>
                    </div>
                    <div class="stat-box" style="background: white; padding: 1rem; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div style="font-size: 0.9rem; color: #6c757d;">Successfully Imported</div>
                        <div style="font-size: 1.5rem; font-weight: 600; color: #28a745;"><?php echo $_SESSION['upload_summary']['success_count']; ?></div>
                    </div>
                    <div class="stat-box" style="background: white; padding: 1rem; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div style="font-size: 0.9rem; color: #6c757d;">Errors Encountered</div>
                        <div style="font-size: 1.5rem; font-weight: 600; color: #dc3545;"><?php echo $_SESSION['upload_summary']['error_count']; ?></div>
                    </div>
                    <div class="stat-box" style="background: white; padding: 1rem; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div style="font-size: 0.9rem; color: #6c757d;">Duplicates Found</div>
                        <div style="font-size: 1.5rem; font-weight: 600; color: #ffc107;"><?php echo $_SESSION['upload_summary']['duplicate_count']; ?></div>
                    </div>
                </div>
            </div>
            <?php unset($_SESSION['upload_summary']); ?>
        <?php endif; ?>

        <div class="header-section">
            <h1 class="page-title">📊 Upload Booking Data (Compact Layout)</h1>
            <form method="POST" enctype="multipart/form-data" class="upload-section">
                <div class="file-upload-container">
                    <div class="file-input-wrapper">
                        <input type="file"
                               name="excel_file"
                               class="file-input"
                               accept=".xlsx,.xls"
                               required>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload"></i>
                    Submit
                </button>
                <a href="../upload/Booking_data_upload_template.xlsx" class="btn btn-secondary" download>
                    <i class="fas fa-download"></i>
                    Download Excel Template
                </a>
            </form>
        </div>

        <div class="header-section" style="margin-top: 2rem;">
            <h1 class="page-title">⚖️ Bulk Weight Update</h1>
            <form method="POST" enctype="multipart/form-data" class="upload-section" action="../process/process_bulk_weight_update.php">
                <div class="file-upload-container">
                    <div class="file-input-wrapper">
                        <input type="file"
                               name="weight_update_file"
                               class="file-input"
                               accept=".xlsx,.xls"
                               required>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-upload"></i>
                    Update Weights
                </button>
                <a href="../upload/Weight_update_template.xlsx" class="btn btn-secondary" download>
                    <i class="fas fa-download"></i>
                    Download Weight Update Template
                </a>
            </form>
        </div>
    </div>
</body>
</html> 