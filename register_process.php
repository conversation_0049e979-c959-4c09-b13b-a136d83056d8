<?php
session_start();
header('Content-Type: application/json');

// Database Connection for InfinityFree
$servername = "localhost";
$username = "u111133901_astra_logistic";
$password = "<PERSON><PERSON><PERSON>@0505";
$database = "u111133901_astra_logistic";

$response = array();

try {
    $conn = new mysqli($servername, $username, $password, $database);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Get JSON data from the request
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);

    if (!$data) {
        throw new Exception("Invalid request data");
    }

    $email = trim($data['email']);
    $password = $data['password'];

    // Check if email already exists
    $checkEmail = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $checkEmail->bind_param("s", $email);
    $checkEmail->execute();
    $checkEmail->store_result();

    if ($checkEmail->num_rows > 0) {
        throw new Exception("Email already registered!");
    }
    $checkEmail->close();

    // Store registration data in session
    $_SESSION['pending_registration'] = [
        'email' => $email,
        'password' => $password
    ];

    $response['success'] = true;
    $response['message'] = "Registration details saved successfully";

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?> 