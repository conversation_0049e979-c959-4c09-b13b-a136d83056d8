<?php
session_start();
include 'db_connect.php';

$conn->set_charset('latin1');

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Initialize variables with default values
$dashboard_data = [];
$total_pending = 0;
$total_paid = 0;
$total_transactions = 0;
$total_pending_cash = 0;
$total_cash_received = 0;
$total_online_received = 0;
$monthly_pending = 0;
$monthly_cash_received = 0;
$monthly_online_received = 0;
$cn_data = [];
$total_cn = 0;
$debug_output = "";

try {
    // Fetch pending payment data
    $query = "SELECT 
                inv_cust as customer,
                inv_paysts as payment_status,
                SUM(inv_value) as total_amount,
                COUNT(*) as record_count
              FROM invoice 
              WHERE username = ?
              GROUP BY inv_cust, inv_paysts
              ORDER BY inv_cust, inv_paysts";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Error preparing invoice query: " . $conn->error);
    }
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    // Process invoice data
    while ($row = $result->fetch_assoc()) {
        $customer = $row['customer'];
        $status = strtolower($row['payment_status']);
        $amount = floatval($row['total_amount']);
        
        if (!isset($dashboard_data[$customer])) {
            $dashboard_data[$customer] = [
                'pending' => 0,
                'paid' => 0,
                'total' => 0
            ];
        }
        
        if ($status === 'pending') {
            $dashboard_data[$customer]['pending'] += $amount;
            $total_pending += $amount;
        } else {
            $dashboard_data[$customer]['paid'] += $amount;
            $total_paid += $amount;
        }
        
        $dashboard_data[$customer]['total'] += $amount;
    }

    // C-Note query
    $cn_query = "SELECT 
                    CASE 
                        WHEN LEFT(cn_number, 1) REGEXP '[0-9]' THEN LEFT(cn_number, 2)
                        ELSE LEFT(cn_number, 1)
                    END as series,
                    COUNT(*) as count
                  FROM cn_entries 
                  WHERE username = ?
                  AND cn_number NOT IN (
                    SELECT docket_no 
                    FROM transactions 
                    WHERE username = ?
                  )
                  GROUP BY 
                    CASE 
                        WHEN LEFT(cn_number, 1) REGEXP '[0-9]' THEN LEFT(cn_number, 2)
                        ELSE LEFT(cn_number, 1)
                    END
                  ORDER BY series";

    $cn_stmt = $conn->prepare($cn_query);
    if (!$cn_stmt) {
        throw new Exception("Error preparing C-Note query: " . $conn->error);
    }
    $cn_stmt->bind_param("ss", $username, $username);
    $cn_stmt->execute();
    $cn_result = $cn_stmt->get_result();

    while ($row = $cn_result->fetch_assoc()) {
        $cn_data[$row['series']] = $row['count'];
        $total_cn += $row['count'];
    }

    // Pending Cash Payments query for last 3 days
    $cash_query = "SELECT 
                    DATE(docket_date) as date,
                    COUNT(*) as count,
                    COALESCE(SUM(CASE WHEN payment_status = 'Pending' THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'Cash-Received' THEN amount ELSE 0 END), 0) as cash_received,
                    COALESCE(SUM(CASE WHEN payment_status = 'Online-Received' THEN amount ELSE 0 END), 0) as online_received
                  FROM transactions 
                  WHERE username = ? 
                  AND entry_type = 'cash'
                  AND docket_date >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
                  GROUP BY DATE(docket_date)
                  ORDER BY date DESC";

    $cash_stmt = $conn->prepare($cash_query);
    if (!$cash_stmt) {
        throw new Exception("Error preparing cash payments query: " . $conn->error);
    }
    $cash_stmt->bind_param("s", $username);
    $cash_stmt->execute();
    $cash_result = $cash_stmt->get_result();

    // Calculate totals for the last 3 days
    $total_count = 0;
    $total_pending_cash = 0;
    $total_cash_received = 0;
    $total_online_received = 0;

    while ($row = $cash_result->fetch_assoc()) {
        $total_count += $row['count'];
        $total_pending_cash += $row['pending_amount'];
        $total_cash_received += $row['cash_received'];
        $total_online_received += $row['online_received'];
    }

    // Reset result pointer
    $cash_result->data_seek(0);

    // New robust query for current month totals
    $current_month = date('Y-m');
    $totals = [
        'Pending' => 0,
        'Cash-Received' => 0,
        'Online-Received' => 0
    ];
    $totals_query = "SELECT payment_status, COALESCE(SUM(amount),0) as total
                     FROM transactions
                     WHERE username = ?
                       AND entry_type = 'cash'
                       AND DATE_FORMAT(docket_date, '%Y-%m') = ?
                     GROUP BY payment_status";
    $totals_stmt = $conn->prepare($totals_query);
    $totals_stmt->bind_param("ss", $username, $current_month);
    $totals_stmt->execute();
    $totals_result = $totals_stmt->get_result();
    while ($row = $totals_result->fetch_assoc()) {
        $status = $row['payment_status'];
        if (isset($totals[$status])) {
            $totals[$status] = $row['total'];
        }
    }
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    // Continue with default values if there's an error
}

// Debug: Print final dashboard data
error_log("Final dashboard data: " . print_r($dashboard_data, true));
error_log("Totals - Pending: $total_pending, Paid: $total_paid");

// Get detailed client data
$client_detail_query = "SELECT 
                        customer,
                        COUNT(*) as transaction_count,
                        SUM(amount) as pending_amount
                       FROM transactions 
                       WHERE username = ? 
                       AND entry_type = 'cash'
                       AND payment_status = 'Pending'
                       GROUP BY customer
                       ORDER BY pending_amount DESC";

$client_stmt = $conn->prepare($client_detail_query);
$client_stmt->bind_param("s", $username);
$client_stmt->execute();
$client_result = $client_stmt->get_result();

// Calculate pending/paid customers and their totals
$pending_customers = 0;
$paid_customers = 0;
$total_pending_amount = 0;
$total_paid_amount = 0;
foreach ($dashboard_data as $customer => $data) {
    if ($data['pending'] > 0) {
        $pending_customers++;
        $total_pending_amount += $data['pending'];
    }
    if ($data['paid'] > 0) {
        $paid_customers++;
        $total_paid_amount += $data['paid'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #4361ee;
            --light-blue: #f1f4ff;
            --hover-blue: #3046c0;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #f8fafc;
            --success-green: #2ec4b6;
            --hover-green: #25a89c;
            --danger-red: #ef476f;
            --hover-red: #d63d63;
            --card-shadow: 0 4px 20px rgba(0,0,0,0.05);
            --hover-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        body {
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            background: var(--background);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            gap: 5mm;
        }

        .dashboard-wrapper {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            gap: 2%;
        }

        .container {
            flex: 0 0 48%;
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        /* Control container order */
        .container:nth-child(1) { order: 1; }
        .container:nth-child(2) { order: 3; }
        .container:nth-child(3) { order: 2; }

        @media (max-width: 1200px) {
            .container {
                flex: 0 0 100%;
                order: unset !important;
            }
        }

        .container:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .container:first-child {
            margin-right: 2%;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.8rem;
            border-bottom: 2px solid var(--light-blue);
        }

        .dashboard-title {
            color: var(--text-dark);
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .dashboard-title i {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem;
            border-radius: 10px;
            font-size: 1.2rem;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .summary-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 12px;
            padding: 0.8rem;
            box-shadow: var(--card-shadow);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 90px;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-blue), var(--hover-blue));
            opacity: 0.8;
        }

        .summary-card.pending::before {
            background: linear-gradient(to right, var(--danger-red), var(--hover-red));
        }

        .summary-card.paid::before {
            background: linear-gradient(to right, var(--success-green), var(--hover-green));
        }

        .summary-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
        }

        .card-title {
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 0.3rem;
            justify-content: center;
        }

        .card-title i {
            font-size: 0.9rem;
        }

        .card-amount {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
        }

        .card-amount.pending {
            color: var(--danger-red);
        }

        .card-amount.paid {
            color: var(--success-green);
        }

        .card-amount.total {
            color: var(--primary-blue);
            background: linear-gradient(45deg, var(--primary-blue), var(--hover-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .customer-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .customer-table th {
            background: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
            font-size: 0.95rem;
            padding: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-align: left;
        }

        .customer-table td {
            padding: 1rem;
            font-size: 0.95rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            text-align: left;
        }
        
        /* Prevent date column from wrapping */
        .customer-table td:first-child {
            white-space: nowrap;
        }

        .customer-table tr:last-child td {
            border-bottom: none;
        }

        .customer-table tr:hover td {
            background: var(--light-blue);
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            transition: all 0.3s ease;
        }

        .status-badge i {
            font-size: 0.8rem;
        }

        .status-badge.pending {
            background: #fff5f7;
            color: var(--danger-red);
        }

        .status-badge.paid {
            background: #f0faf9;
            color: var(--success-green);
        }

        .status-badge.total {
            background: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .total-row {
            background: var(--light-blue);
            font-weight: 500;
        }

        .total-row:hover td {
            background: var(--light-blue) !important;
        }

        .total-row td {
            border-top: 2px solid var(--border-color);
            border-bottom: 2px solid var(--border-color);
        }

        .metric-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .metric-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.07);
            padding: 0.8rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 80px;
            transition: box-shadow 0.2s, transform 0.2s;
            position: relative;
        }

        .metric-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.13);
            transform: translateY(-2px) scale(1.03);
        }

        .metric-icon {
            font-size: 1.2rem;
            margin-bottom: 0.3rem;
        }

        .pending-metric .metric-icon, .pending-amount-metric .metric-icon {
            color: var(--danger-red);
        }

        .paid-metric .metric-icon, .paid-amount-metric .metric-icon {
            color: var(--success-green);
        }

        .pending-metric {
            background: #fff5f7;
        }

        .pending-amount-metric {
            background: #ffeaea;
        }

        .paid-metric {
            background: #f0faf9;
        }

        .paid-amount-metric {
            background: #e6fff7;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #333;
            margin-bottom: 0.2rem;
            font-weight: 500;
        }

        .metric-value {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary-blue);
        }

        @media (max-width: 900px) {
            .metric-cards {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .summary-cards {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 0.8rem;
            }

            .summary-cards {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }

            .metric-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .card-title {
                font-size: 0.8rem;
            }

            .card-amount {
                font-size: 0.95rem;
            }

            .metric-label {
                font-size: 0.8rem;
            }

            .metric-value {
                font-size: 0.9rem;
            }
            
            .metric-icon {
                font-size: 1.1rem;
            }
        }

        @media (min-width: 1201px) {
            .container {
                width: calc(48% - 2rem);
            }
            
            .container:nth-child(2) {
                width: calc(48% - 2rem + 10mm);
            }
        }

        .view-all-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 1.5rem;
            padding: 0 1rem;
        }

        .view-all-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: var(--primary-blue);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(67, 97, 238, 0.2);
        }

        .view-all-btn:hover {
            background: var(--hover-blue);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
        }

        .view-all-btn i {
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .view-all-container {
                justify-content: center;
                padding: 0;
            }

            .view-all-btn {
                width: 100%;
                justify-content: center;
            }
        }

        .cn-buttons-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            padding: 1rem 0;
        }

        .cn-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.8rem;
            border-radius: 10px;
            border: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: default;
        }

        .cn-button:nth-child(5n+1) { background: #4361ee; }
        .cn-button:nth-child(5n+2) { background: #2ec4b6; }
        .cn-button:nth-child(5n+3) { background: #ff6b6b; }
        .cn-button:nth-child(5n+4) { background: #845ef7; }
        .cn-button:nth-child(5n+5) { background: #40c057; }

        .cn-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .cn-series {
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
        }

        .cn-count {
            font-size: 1.3rem;
            font-weight: 600;
        }

        /* Reset previous gap styles */
        .container:first-of-type {
            margin-bottom: 0;
        }

        /* Add specific gap after Payment Dashboard */
        body > .container:nth-child(1) {
            margin-bottom: 15mm !important;
        }

        @media (max-width: 1200px) {
            .container {
                width: 100%;
                margin: 1rem 0;
            }
            
            body > .container:nth-child(1) {
                margin-bottom: 15mm !important;
            }
        }

        .dashboard-payment {
            margin-bottom: 15mm !important;
        }
    </style>
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- First Row -->
        <div class="container">
            <div class="dashboard-header">
                <h1 class="dashboard-title">
                    <i class="fas fa-chart-line"></i>
                    Payment Dashboard
                </h1>
            </div>
            <!-- Payment Dashboard content -->
            <div class="summary-cards">
                <div class="summary-card pending">
                    <h3 class="card-title">
                        <i class="fas fa-clock"></i>
                        Pending Amount
                    </h3>
                    <p class="card-amount pending">₹<?php echo number_format($total_pending, 2); ?></p>
                </div>
                <div class="summary-card paid">
                    <h3 class="card-title">
                        <i class="fas fa-check-circle"></i>
                        Paid
                    </h3>
                    <p class="card-amount paid">₹<?php echo number_format($total_paid, 2); ?></p>
                </div>
                <div class="summary-card total">
                    <h3 class="card-title">
                        <i class="fas fa-wallet"></i>
                        Total
                    </h3>
                    <p class="card-amount total">₹<?php echo number_format($total_pending + $total_paid, 2); ?></p>
                </div>
            </div>

            <!-- Metric Summary Cards -->
            <div class="metric-cards">
                <div class="metric-card pending-metric">
                    <div class="metric-icon"><i class="fas fa-user-clock"></i></div>
                    <div class="metric-label">Pending Customers</div>
                    <div class="metric-value"><?php echo $pending_customers; ?></div>
                </div>
                <div class="metric-card pending-amount-metric">
                    <div class="metric-icon"><i class="fas fa-rupee-sign"></i></div>
                    <div class="metric-label">Total Pending Amount</div>
                    <div class="metric-value">₹<?php echo number_format($total_pending_amount, 2); ?></div>
                </div>
                <div class="metric-card paid-metric">
                    <div class="metric-icon"><i class="fas fa-user-check"></i></div>
                    <div class="metric-label">Paid Customers</div>
                    <div class="metric-value"><?php echo $paid_customers; ?></div>
                </div>
                <div class="metric-card paid-amount-metric">
                    <div class="metric-icon"><i class="fas fa-rupee-sign"></i></div>
                    <div class="metric-label">Total Paid Amount</div>
                    <div class="metric-value">₹<?php echo number_format($total_paid_amount, 2); ?></div>
                </div>
            </div>
            <div class="view-all-container">
                <a href="index.php?page=bills_report" class="view-all-btn">
                    <i class="fas fa-file-invoice"></i>
                    View All Bill Details
                </a>
            </div>
        </div>

        <div class="container">
            <div class="dashboard-header">
                <h1 class="dashboard-title">
                    <i class="fas fa-file-alt"></i>
                    C-Note Details
                </h1>
            </div>
            <!-- C-Note Details content -->
            <div class="summary-cards">
                <div class="summary-card total">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice"></i>
                        Total C-Notes
                    </h3>
                    <p class="card-amount total"><?php echo $total_cn; ?></p>
                </div>
            </div>

            <div class="cn-buttons-container">
                <?php foreach ($cn_data as $series => $count) { ?>
                    <div class="cn-button">
                        <span class="cn-series"><?php echo htmlspecialchars($series); ?> Series</span>
                        <span class="cn-count"><?php echo $count; ?></span>
                    </div>
                <?php } ?>
            </div>
        </div>

        <!-- Second Row -->
        <div class="container">
            <div class="dashboard-header">
                <h1 class="dashboard-title">
                    <i class="fas fa-money-bill-wave"></i>
                    Monthly Cash Summary (<?php echo date('M Y'); ?>)
                </h1>
            </div>
            <!-- Monthly Cash Summary content -->
            <div class="summary-cards">
                <div class="summary-card pending">
                    <h3 class="card-title">
                        <i class="fas fa-clock"></i>
                        Pending Amount
                    </h3>
                    <p class="card-amount pending">₹<?php echo number_format($totals['Pending'], 2); ?></p>
                </div>
                <div class="summary-card paid">
                    <h3 class="card-title">
                        <i class="fas fa-money-bill-wave"></i>
                        Cash Received
                    </h3>
                    <p class="card-amount paid">₹<?php echo number_format($totals['Cash-Received'], 2); ?></p>
                </div>
                <div class="summary-card total">
                    <h3 class="card-title">
                        <i class="fas fa-credit-card"></i>
                        Online Received
                    </h3>
                    <p class="card-amount total">₹<?php echo number_format($totals['Online-Received'], 2); ?></p>
                </div>
            </div>

            <table class="customer-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Count</th>
                        <th>Pending Amount</th>
                        <th>Cash Received</th>
                        <th>Online Received</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $cash_result->fetch_assoc()) { ?>
                        <tr>
                            <td style="white-space: nowrap;"><?php echo date('d M Y', strtotime($row['date'])); ?></td>
                            <td><?php echo $row['count']; ?></td>
                            <td>₹<?php echo number_format($row['pending_amount'], 2); ?></td>
                            <td>₹<?php echo number_format($row['cash_received'], 2); ?></td>
                            <td>₹<?php echo number_format($row['online_received'], 2); ?></td>
                        </tr>
                    <?php } ?>
                    <tr class="total-row">
                        <td style="text-align: right; font-weight: bold;">Totals:</td>
                        <td style="font-weight: bold;"><?php echo $total_count; ?></td>
                        <td style="font-weight: bold;">₹<?php echo number_format($total_pending_cash, 2); ?></td>
                        <td style="font-weight: bold;">₹<?php echo number_format($total_cash_received, 2); ?></td>
                        <td style="font-weight: bold;">₹<?php echo number_format($total_online_received, 2); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
<?php if (!empty($debug_output)) { ?>
<pre style="background:#222;color:#0f0;padding:1em 2em;font-size:1em;overflow:auto;max-width:1200px;margin:2em auto;border-radius:8px;">
<?php echo htmlspecialchars($debug_output); ?>
</pre>
<?php } ?>
</html> 