<?php
// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit;
}

// Use the connection from the existing session
// DB is already connected in the parent file

// Get user information
$username = $_SESSION['username'];
$userRole = $_SESSION['role'] ?? 'user';
$isAdmin = ($userRole === 'admin');

// Process AJAX requests for data updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'update') {
        $id = $_POST['id'];
        $field = $_POST['field'];
        $value = $_POST['value'];
        
        // Sanitize field name to prevent SQL injection
        $allowedFields = [
            'entry_type', 'customer', 'docket_no', 'docket_date', 'pincode', 
            'destination', 'weight', 'mode_of_tsp', 'waybill_value', 'remarks', 
            'amount', 'entry_ts', 'waybill_percent', 'oda_chrg', 'owner_risk', 
            'carrier_risk', 'payment_status', 'payment_received_date', 'waybill', 
            'mobile1', 'mobile2'
        ];
        
        if (!in_array($field, $allowedFields)) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid field']);
            exit;
        }
        
        // Prepare and execute the update query
        try {
            $sql = "UPDATE transactions SET $field = ? WHERE id = ?";
            
            // Special handling for date fields
            if ($field === 'docket_date' || $field === 'payment_received_date') {
                if (empty($value)) {
                    $value = null;
                }
            }
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('si', $value, $id);
            $result = $stmt->execute();
            
            if ($result) {
                echo json_encode(['status' => 'success']);
            } else {
                echo json_encode(['status' => 'error', 'message' => $conn->error]);
            }
        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
        exit;
    }
}

// Define columns to display - include all available fields
$columns = [
    'id' => 'ID',
    'entry_type' => 'Type',
    'customer' => 'Customer',
    'docket_no' => 'Docket No',
    'docket_date' => 'Docket Date',
    'pincode' => 'Pincode',
    'destination' => 'Destination',
    'weight' => 'Weight',
    'mode_of_tsp' => 'Mode',
    'waybill_value' => 'Waybill Value',
    'remarks' => 'Remarks',
    'amount' => 'Amount',
    'entry_ts' => 'TS Fee',
    'waybill_percent' => 'WB %',
    'oda_chrg' => 'ODA',
    'owner_risk' => 'Owner Risk',
    'carrier_risk' => 'Carrier Risk',
    'payment_status' => 'Payment Status',
    'payment_received_date' => 'Payment Date',
    'waybill' => 'Waybill',
    'mobile1' => 'Mobile 1',
    'mobile2' => 'Mobile 2',
    'username' => 'Created By',
    'created_at' => 'Created At'
];

// Build the query based on filters
$whereClause = [];
$params = [];
$paramTypes = '';

// Apply search if provided
$searchTerm = '';
if (isset($_GET['search']) && $_GET['search'] !== '') {
    $searchTerm = $_GET['search'];
    $searchConditions = [];
    
    // Apply search to all searchable columns
    foreach ($columns as $key => $label) {
        if (in_array($key, ['id', 'entry_type', 'customer', 'docket_no', 'destination', 'pincode', 'mode_of_tsp', 'payment_status', 'username'])) {
            $searchConditions[] = "$key LIKE ?";
            $params[] = '%' . $searchTerm . '%';
            $paramTypes .= 's';
        }
    }
    
    if (!empty($searchConditions)) {
        $whereClause[] = '(' . implode(' OR ', $searchConditions) . ')';
    }
}

// Add username filter for non-admin users
if (!$isAdmin) {
    $whereClause[] = "username = ?";
    $params[] = $username;
    $paramTypes .= 's';
}

$whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';

// Determine the number of records to fetch
$recordsPerPage = 50;
$page = isset($_GET['p']) ? intval($_GET['p']) : 1;
$offset = ($page - 1) * $recordsPerPage;
$limit = " LIMIT $offset, $recordsPerPage";

// Prepare the SQL query with optimized ordering (only by id)
$sql = "SELECT * FROM transactions $whereStr ORDER BY id DESC $limit";

// Get total count for pagination
$countSql = "SELECT COUNT(*) as total FROM transactions $whereStr";
$totalRecords = 0;

if (!empty($params)) {
    $stmt = $conn->prepare($countSql);
    $stmt->bind_param($paramTypes, ...$params);
    $stmt->execute();
    $countResult = $stmt->get_result();
    if ($countResult && $row = $countResult->fetch_assoc()) {
        $totalRecords = $row['total'];
    }
} else {
    $countResult = $conn->query($countSql);
    if ($countResult && $row = $countResult->fetch_assoc()) {
        $totalRecords = $row['total'];
    }
}

// Calculate total pages
$totalPages = ceil($totalRecords / $recordsPerPage);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions Viewer</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .table-responsive {
            overflow-x: auto;
        }
        table {
            font-size: 0.9rem;
        }
        .editable {
            cursor: pointer;
            position: relative;
        }
        .editable:hover {
            background-color: #f8f9fa;
        }
        .editable:hover::after {
            content: '\f044';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 5px;
            color: #6c757d;
            font-size: 0.8rem;
        }
        .editable.editing {
            padding: 0 !important;
        }
        .editable.editing input,
        .editable.editing select,
        .editable.editing textarea {
            width: 100%;
            height: 100%;
            padding: 8px;
            border: 1px solid #0d6efd;
            background-color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            outline: none;
        }
        .cash {
            background-color: #d1e7dd !important;
        }
        .credit {
            background-color: #cfe2ff !important;
        }
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }
        .inline-save-btn, .inline-cancel-btn {
            position: absolute;
            right: 0;
            background: #fff;
            border: 1px solid #ccc;
            padding: 1px 5px;
            font-size: 0.8rem;
            z-index: 100;
        }
        .inline-save-btn {
            top: 0;
            color: green;
        }
        .inline-cancel-btn {
            top: 20px;
            color: red;
        }
        .edit-success {
            animation: flash-green 1s;
        }
        @keyframes flash-green {
            0% { background-color: #d1e7dd; }
            70% { background-color: #d1e7dd; }
            100% { background-color: inherit; }
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <h2 class="mb-3">Transactions Viewer</h2>
        
        <!-- Simple Search Box -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="index.php" id="searchForm" class="row g-3 align-items-center">
                    <input type="hidden" name="page" value="transactions_view">
                    
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Search in all fields..." value="<?php echo htmlspecialchars($searchTerm ?? ''); ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <?php if (!empty($searchTerm)): ?>
                                <a href="index.php?page=transactions_view" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6 text-end">
                        <?php if ($totalRecords > 0): ?>
                            <span class="text-muted">
                                Showing <?php echo $offset+1; ?> to <?php echo min($offset+$recordsPerPage, $totalRecords); ?> 
                                of <?php echo $totalRecords; ?> records
                            </span>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <?php foreach ($columns as $key => $label): ?>
                            <th><?php echo $label; ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if (!empty($params)) {
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param($paramTypes, ...$params);
                        $stmt->execute();
                        $result = $stmt->get_result();
                    } else {
                        $result = $conn->query($sql);
                    }
                    
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            $rowClass = $row['entry_type'] == 'cash' ? 'cash' : 'credit';
                            echo "<tr class='$rowClass'>";
                            foreach ($columns as $key => $label) {
                                $value = htmlspecialchars($row[$key] ?? '');
                                
                                // Format the date columns
                                if ($key === 'docket_date' || $key === 'payment_received_date' || $key === 'created_at') {
                                    if ($value && $value !== '0000-00-00') {
                                        $dateObj = new DateTime($value);
                                        $value = $dateObj->format('Y-m-d');
                                    } else {
                                        $value = '';
                                    }
                                }
                                
                                // Special handling for payment_status
                                if ($key === 'payment_status') {
                                    $statusClass = '';
                                    if ($value === 'Cash-Received' || $value === 'Online-Received') {
                                        $statusClass = 'text-success';
                                    } elseif ($value === 'Pending') {
                                        $statusClass = 'text-danger';
                                    }
                                    echo "<td class='$statusClass'>$value</td>";
                                } 
                                // Make certain fields editable
                                elseif (in_array($key, ['customer', 'pincode', 'destination', 'weight', 'mode_of_tsp', 
                                                     'waybill_value', 'remarks', 'amount', 'payment_status', 
                                                     'payment_received_date', 'mobile1', 'mobile2']) && 
                                        ($isAdmin || ($row['username'] === $username))) {
                                    echo "<td class='editable' data-id='{$row['id']}' data-field='$key'>$value</td>";
                                } else {
                                    echo "<td>$value</td>";
                                }
                            }
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='" . count($columns) . "' class='text-center'>No transactions found</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
        
        <?php if (!isset($_GET['filter_submit']) && $result && $result->num_rows >= $recordsPerPage): ?>
            <div class="alert alert-info">
                Showing records <?php echo $offset+1; ?> to <?php echo min($offset+$recordsPerPage, $totalRecords); ?> of <?php echo $totalRecords; ?> total records.
                Apply filters to see more specific data.
            </div>
        <?php endif; ?>
        
        <div id="recordCount" class="text-muted small mb-3">
            <?php 
            if ($result) {
                echo "Records found: " . $totalRecords;
            } 
            ?>
        </div>
        
        <!-- Pagination controls -->
        <?php if ($totalPages > 1): ?>
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl(1); ?>" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl($page - 1); ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                <?php endif; ?>
                
                <?php
                // Determine which page numbers to show
                $startPage = max(1, $page - 2);
                $endPage = min($totalPages, $page + 2);
                
                // Always show at least 5 pages if available
                if ($endPage - $startPage + 1 < 5) {
                    if ($startPage == 1) {
                        $endPage = min($totalPages, $startPage + 4);
                    } elseif ($endPage == $totalPages) {
                        $startPage = max(1, $endPage - 4);
                    }
                }
                
                for ($i = $startPage; $i <= $endPage; $i++): ?>
                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo buildPaginationUrl($i); ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl($page + 1); ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo buildPaginationUrl($totalPages); ?>" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Modal for editing -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Field</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <input type="hidden" id="editField">
                        <div class="mb-3" id="editControlContainer">
                            <!-- Dynamic content will be inserted here -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEdit">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize modal
            let editModal;
            if (document.getElementById('editModal')) {
                editModal = new bootstrap.Modal(document.getElementById('editModal'));
            }

            // Handle double-click on editable cells
            $('.editable').on('dblclick', function(e) {
                e.stopPropagation();
                const $cell = $(this);
                
                // Prevent editing multiple cells at once
                if ($('.editable.editing').length > 0) {
                    return;
                }
                
                const id = $cell.data('id');
                const field = $cell.data('field');
                const currentValue = $cell.text().trim();
                
                $cell.addClass('editing');
                
                // Create appropriate input based on field type
                let inputHtml = '';
                
                if (field === 'payment_status') {
                    inputHtml = `<select>
                        <option value="Pending" ${currentValue === 'Pending' ? 'selected' : ''}>Pending</option>
                        <option value="Cash-Received" ${currentValue === 'Cash-Received' ? 'selected' : ''}>Cash-Received</option>
                        <option value="Online-Received" ${currentValue === 'Online-Received' ? 'selected' : ''}>Online-Received</option>
                    </select>`;
                } else if (field === 'docket_date' || field === 'payment_received_date') {
                    inputHtml = `<input type="date" value="${currentValue}">`;
                } else if (field === 'entry_type') {
                    inputHtml = `<select>
                        <option value="cash" ${currentValue === 'cash' ? 'selected' : ''}>Cash</option>
                        <option value="credit" ${currentValue === 'credit' ? 'selected' : ''}>Credit</option>
                    </select>`;
                } else if (field === 'mode_of_tsp') {
                    inputHtml = `<select>
                        <option value="Express" ${currentValue === 'Express' ? 'selected' : ''}>Express</option>
                        <option value="Surface" ${currentValue === 'Surface' ? 'selected' : ''}>Surface</option>
                        <option value="Premium" ${currentValue === 'Premium' ? 'selected' : ''}>Premium</option>
                        <option value="Air Cargo" ${currentValue === 'Air Cargo' ? 'selected' : ''}>Air Cargo</option>
                    </select>`;
                } else if (field === 'weight' || field === 'amount' || field === 'waybill_value' || 
                           field === 'entry_ts' || field === 'waybill_percent' || field === 'oda_chrg' || 
                           field === 'owner_risk' || field === 'carrier_risk') {
                    inputHtml = `<input type="number" step="0.01" value="${currentValue}">`;
                } else if (field === 'remarks') {
                    inputHtml = `<textarea rows="3">${currentValue}</textarea>`;
                } else {
                    inputHtml = `<input type="text" value="${currentValue}">`;
                }
                
                const $input = $(inputHtml);
                const $saveBtn = $('<button class="inline-save-btn" title="Save"><i class="fas fa-check"></i></button>');
                const $cancelBtn = $('<button class="inline-cancel-btn" title="Cancel"><i class="fas fa-times"></i></button>');
                
                $cell.html('').append($input).append($saveBtn).append($cancelBtn);
                $input.focus();
                
                // Handle save button click
                $saveBtn.on('click', function() {
                    saveEdit($cell, id, field, $input.val() || $input.find('option:selected').val());
                });
                
                // Handle cancel button click
                $cancelBtn.on('click', function() {
                    $cell.removeClass('editing').html(currentValue);
                });
                
                // Handle Enter key press
                $input.on('keydown', function(e) {
                    if (e.keyCode === 13) { // Enter key
                        e.preventDefault();
                        saveEdit($cell, id, field, $input.val() || $input.find('option:selected').val());
                    } else if (e.keyCode === 27) { // Escape key
                        $cell.removeClass('editing').html(currentValue);
                    }
                });
                
                // Handle click outside
                $(document).on('click.editCancel', function(event) {
                    if (!$(event.target).closest('.editable.editing, .inline-save-btn, .inline-cancel-btn').length) {
                        $cell.removeClass('editing').html(currentValue);
                        $(document).off('click.editCancel');
                    }
                });
            });

            // Function to save edited value
            function saveEdit($cell, id, field, value) {
                const originalText = $cell.text();
                
                // Show loading indicator
                $cell.removeClass('editing').html('<i class="fas fa-spinner fa-spin"></i>');
                
                $.ajax({
                    url: 'index.php?page=transactions_view',
                    method: 'POST',
                    data: {
                        action: 'update',
                        id: id,
                        field: field,
                        value: value
                    },
                    success: function(response) {
                        try {
                            // Try to parse the response if it's a string
                            if (typeof response === 'string') {
                                response = JSON.parse(response);
                            }
                            
                            if (response.status === 'success') {
                                $cell.text(value);
                                $cell.addClass('edit-success');
                                setTimeout(() => $cell.removeClass('edit-success'), 1000);
                                
                                // If changing entry_type, update the row class
                                if (field === 'entry_type') {
                                    const $row = $cell.closest('tr');
                                    $row.removeClass('cash credit').addClass(value);
                                }
                            } else {
                                $cell.text(originalText);
                                showErrorAlert('Error: ' + (response.message || 'Unknown error'));
                            }
                        } catch (e) {
                            $cell.text(originalText);
                            showErrorAlert('Invalid response from server');
                            console.error(e, response);
                        }
                    },
                    error: function(xhr) {
                        $cell.text(originalText);
                        showErrorAlert('Network error: ' + xhr.statusText);
                    }
                });
                
                $(document).off('click.editCancel');
            }
            
            // Helper function to show error alert
            function showErrorAlert(message) {
                const alertDiv = $('<div>')
                    .addClass('alert alert-danger alert-dismissible fade show')
                    .attr('role', 'alert')
                    .text(message)
                    .append(
                        $('<button>')
                            .addClass('btn-close')
                            .attr('type', 'button')
                            .attr('data-bs-dismiss', 'alert')
                            .attr('aria-label', 'Close')
                    );
                
                $('.container-fluid').prepend(alertDiv);
                
                setTimeout(function() {
                    alertDiv.alert('close');
                }, 3000);
            }
            
            // The existing modal edit code remains as a fallback
            if ($('#saveEdit').length) {
                $('#saveEdit').on('click', function() {
                    const id = $('#editId').val();
                    const field = $('#editField').val();
                    const value = $('#editValue').val();
                    
                    $.ajax({
                        url: 'index.php?page=transactions_view',
                        method: 'POST',
                        data: {
                            action: 'update',
                            id: id,
                            field: field,
                            value: value
                        },
                        success: function(response) {
                            try {
                                // Try to parse the response if it's a string
                                if (typeof response === 'string') {
                                    response = JSON.parse(response);
                                }
                                
                                if (response.status === 'success') {
                                    editModal.hide();
                                    
                                    // Update the cell with new value
                                    $(`.editable[data-id="${id}"][data-field="${field}"]`).text(value);
                                    
                                    // Show a success message
                                    const alertDiv = $('<div>')
                                        .addClass('alert alert-success alert-dismissible fade show')
                                        .attr('role', 'alert')
                                        .text('Updated successfully')
                                        .append(
                                            $('<button>')
                                                .addClass('btn-close')
                                                .attr('type', 'button')
                                                .attr('data-bs-dismiss', 'alert')
                                                .attr('aria-label', 'Close')
                                        );
                                    
                                    $('.container-fluid').prepend(alertDiv);
                                    
                                    // Auto-dismiss after 2 seconds
                                    setTimeout(function() {
                                        alertDiv.alert('close');
                                    }, 2000);
                                } else {
                                    alert('Error: ' + (response.message || 'Unknown error'));
                                }
                            } catch (e) {
                                alert('Invalid response from server');
                                console.error(e, response);
                            }
                        },
                        error: function(xhr) {
                            alert('Network error: ' + xhr.statusText);
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>

<?php
// Helper function to build pagination URLs that preserve existing parameters
function buildPaginationUrl($pageNum) {
    $params = $_GET;
    $params['p'] = $pageNum;
    return 'index.php?' . http_build_query($params);
}
?> 