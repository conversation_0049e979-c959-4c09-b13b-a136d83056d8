<?php
require_once dirname(dirname(__FILE__)) . '/db_connect.php';

function generateInvoiceNumber($username, $customer = null) {
    global $conn;
    
    try {
        error_log("Generating invoice number for user: " . $username . " and customer: " . $customer);
        
        // Check if customer's GST is applicable
        $gst_apl = 'Yes';
        if ($customer) {
            $gst_stmt = $conn->prepare("SELECT gst_apl FROM customers WHERE short_name = ?");
            if (!$gst_stmt) {
                error_log("Error preparing GST query: " . $conn->error);
                throw new Exception("Database error: " . $conn->error);
            }
            
            $gst_stmt->bind_param("s", $customer);
            if (!$gst_stmt->execute()) {
                error_log("Error executing GST query: " . $gst_stmt->error);
                throw new Exception("Database error: " . $gst_stmt->error);
            }
            
            $gst_result = $gst_stmt->get_result();
            if ($gst_row = $gst_result->fetch_assoc()) {
                $gst_apl = $gst_row['gst_apl'];
                error_log("Customer GST applicability: " . $gst_apl);
            } else {
                error_log("No GST applicability found for customer: " . $customer);
            }
        }
        
        // Get user's invoice settings
        $stmt = $conn->prepare("SELECT invoice_series, invoice_number, manual_series, manual_invoice_number FROM settings WHERE username = ?");
        if (!$stmt) {
            error_log("Error preparing invoice number query: " . $conn->error);
            throw new Exception("Database error: " . $conn->error);
        }
        
        $stmt->bind_param("s", $username);
        if (!$stmt->execute()) {
            error_log("Error executing invoice number query: " . $stmt->error);
            throw new Exception("Database error: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $settings = $result->fetch_assoc();
        
        if (!$settings) {
            error_log("No settings found for user: " . $username);
            // Initialize with default values
            $settings = [
                'invoice_series' => '',
                'invoice_number' => '0001',
                'manual_series' => '',
                'manual_invoice_number' => '0001'
            ];
            
            // Insert default settings
            $insert_stmt = $conn->prepare("INSERT INTO settings (username, invoice_series, invoice_number, manual_series, manual_invoice_number) VALUES (?, '', '0001', '', '0001')");
            if (!$insert_stmt) {
                error_log("Error preparing insert settings query: " . $conn->error);
                throw new Exception("Database error: " . $conn->error);
            }
            
            $insert_stmt->bind_param("s", $username);
            if (!$insert_stmt->execute()) {
                error_log("Error inserting default settings: " . $insert_stmt->error);
                throw new Exception("Database error: " . $insert_stmt->error);
            }
        }
        
        // Choose series and number based on GST applicability
        if ($gst_apl == 'No') {
            $invoice_series = $settings['manual_series'] ?? '';
            $current_number = $settings['manual_invoice_number'] ?? '0001';
            $update_field = 'manual_invoice_number';
            error_log("Using manual series: " . $invoice_series . " and number: " . $current_number);
        } else {
            $invoice_series = $settings['invoice_series'] ?? '';
            $current_number = $settings['invoice_number'] ?? '0001';
            $update_field = 'invoice_number';
            error_log("Using regular series: " . $invoice_series . " and number: " . $current_number);
        }
        
        // Generate the full invoice number
        $invoice_number = $invoice_series . str_pad($current_number, 4, '0', STR_PAD_LEFT);
        error_log("Generated invoice number: " . $invoice_number);
        
        // Update the invoice number for next time
        $next_number = str_pad(intval($current_number) + 1, 4, '0', STR_PAD_LEFT);
        $update_stmt = $conn->prepare("UPDATE settings SET $update_field = ? WHERE username = ?");
        
        if (!$update_stmt) {
            error_log("Error preparing update settings query: " . $conn->error);
            throw new Exception("Database error: " . $conn->error);
        }
        
        $update_stmt->bind_param("ss", $next_number, $username);
        if (!$update_stmt->execute()) {
            error_log("Error updating invoice number: " . $update_stmt->error);
            throw new Exception("Database error: " . $update_stmt->error);
        }
        
        error_log("Successfully updated invoice number to: " . $next_number);
        
        return $invoice_number;
        
    } catch (Exception $e) {
        error_log("Error in generateInvoiceNumber: " . $e->getMessage());
        throw $e;
    }
}

function getNextInvoiceNumber($username, $customer = null) {
    global $conn;
    
    try {
        error_log("Getting next invoice number for user: " . $username . " and customer: " . $customer);
        
        // Check if customer's GST is applicable
        $gst_apl = 'Yes';
        if ($customer) {
            $gst_stmt = $conn->prepare("SELECT gst_apl FROM customers WHERE short_name = ?");
            if (!$gst_stmt) {
                error_log("Error preparing GST query: " . $conn->error);
                throw new Exception("Database error: " . $conn->error);
            }
            
            $gst_stmt->bind_param("s", $customer);
            if (!$gst_stmt->execute()) {
                error_log("Error executing GST query: " . $gst_stmt->error);
                throw new Exception("Database error: " . $gst_stmt->error);
            }
            
            $gst_result = $gst_stmt->get_result();
            if ($gst_row = $gst_result->fetch_assoc()) {
                $gst_apl = $gst_row['gst_apl'];
                error_log("Customer GST applicability: " . $gst_apl);
            } else {
                error_log("No GST applicability found for customer: " . $customer);
            }
        }
        
        // Get user's invoice settings
        $stmt = $conn->prepare("SELECT invoice_series, invoice_number, manual_series, manual_invoice_number FROM settings WHERE username = ?");
        if (!$stmt) {
            error_log("Error preparing next invoice number query: " . $conn->error);
            throw new Exception("Database error: " . $conn->error);
        }
        
        $stmt->bind_param("s", $username);
        if (!$stmt->execute()) {
            error_log("Error executing next invoice number query: " . $stmt->error);
            throw new Exception("Database error: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $settings = $result->fetch_assoc();
        
        if (!$settings) {
            error_log("No settings found for user: " . $username);
            return "0001";
        } else {
            error_log("Found settings: " . json_encode($settings));
        }
        
        // Choose series and number based on GST applicability
        if ($gst_apl == 'No') {
            $invoice_series = $settings['manual_series'] ?? '';
            $current_number = $settings['manual_invoice_number'] ?? '0001';
            error_log("Using manual series: " . $invoice_series . " and number: " . $current_number);
        } else {
            $invoice_series = $settings['invoice_series'] ?? '';
            $current_number = $settings['invoice_number'] ?? '0001';
            error_log("Using regular series: " . $invoice_series . " and number: " . $current_number);
        }
        
        // Don't increment, just return the current number
        $next_number = $invoice_series . str_pad($current_number, 4, '0', STR_PAD_LEFT);
        error_log("Next invoice number will be: " . $next_number);
        
        return $next_number;
        
    } catch (Exception $e) {
        error_log("Error in getNextInvoiceNumber: " . $e->getMessage());
        return "ERROR-" . date('YmdHis'); // Fallback invoice number
    }
}

// Add function to validate invoice data
function validateInvoiceData($data) {
    $errors = [];
    
    // Required fields
    $required_fields = ['customer', 'amount', 'username'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $errors[] = "Missing required field: $field";
        }
    }
    
    // Validate amounts
    if (isset($data['amount']) && is_array($data['amount'])) {
        foreach ($data['amount'] as $index => $amount) {
            if (!is_numeric($amount) || $amount < 0) {
                $errors[] = "Invalid amount at index $index: $amount";
            }
        }
    }
    
    // Validate dates if present
    if (isset($data['from_date']) && !strtotime($data['from_date'])) {
        $errors[] = "Invalid from_date format";
    }
    if (isset($data['to_date']) && !strtotime($data['to_date'])) {
        $errors[] = "Invalid to_date format";
    }
    
    return $errors;
} 