<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    try {
        // Only allow deletion if the rate belongs to the logged-in user
        $sql = "DELETE FROM ts_rate_master WHERE id = ? AND username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("is", $id, $username);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo "<script>
                    alert('Rate deleted successfully!');
                    window.location.href='index.php?page=ts_rate_master';
                </script>";
            } else {
                throw new Exception("Rate not found or you don't have permission to delete it.");
            }
        } else {
            throw new Exception($stmt->error);
        }
    } catch (Exception $e) {
        echo "<script>
            alert('Error: " . addslashes($e->getMessage()) . "');
            window.location.href='index.php?page=ts_rate_master';
        </script>";
    }
} else {
    header("Location: index.php?page=ts_rate_master");
    exit();
}
?> 