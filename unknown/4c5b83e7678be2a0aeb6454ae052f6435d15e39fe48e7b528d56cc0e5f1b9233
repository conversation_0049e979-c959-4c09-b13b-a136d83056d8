-- SQL script to add C-Note validation toggle column to settings table
-- This adds an optional validation toggle for docket number validation in cash-entry.php and credit-entry.php

-- Add the new column for C-Note validation toggle
ALTER TABLE `settings` 
ADD COLUMN `cnote_validation_enabled` TINYINT(1) NOT NULL DEFAULT 1 
COMMENT 'Enable/Disable C-Note validation (1=enabled, 0=disabled)' 
AFTER `ecom_surface_docket`;

-- Update existing records to have validation enabled by default (maintains current behavior)
UPDATE `settings` SET `cnote_validation_enabled` = 1 WHERE `cnote_validation_enabled` IS NULL;

-- Verify the column was added successfully
-- SELECT username, cnote_validation_enabled FROM settings;
