<?php 
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];
$editMode = false;
$rate = [
    'id' => '',
    'short_name' => '',
    'mode_of_tsp' => '',
    'zone' => '',
    'up_to_0250' => '',
    'up_to_0500' => '',
    'addl_500gm' => '',
    'above_3kg' => ''
];

if (isset($_GET['id'])) {
    $editMode = true;
    $id = $_GET['id'];
    $stmt = $conn->prepare("SELECT *, CASE WHEN mode_of_tsp = 'Premium' THEN pr_zone ELSE zone END as display_zone FROM rate_master WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $id, $username);
    $stmt->execute();
    $result = $stmt->get_result();
    $rate = $result->fetch_assoc();
}

$sql = "SELECT r.*, 
        CASE 
            WHEN r.mode_of_tsp = 'Premium' THEN r.pr_zone 
            ELSE r.zone 
        END as display_zone,
        CASE 
            WHEN r.mode_of_tsp = 'Premium' THEN r.pr_zone 
            ELSE r.zone 
        END as zone
        FROM rate_master r 
        WHERE r.username = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rate Master</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            font-family: Arial, sans-serif;
            background: var(--background);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        h2, h3 {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        form {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .button-group {
            grid-column: 1 / -1;
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        select, input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: var(--background);
            height: 42px;
            box-sizing: border-box;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        button[type="submit"] {
            background: var(--primary-blue);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 150px;
        }

        button[type="submit"]:hover {
            background: var(--hover-blue);
            transform: translateY(-1px);
        }

        button[type="submit"]:active {
            transform: translateY(0);
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: var(--background);
        }

        button {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            margin: 0 0.25rem;
        }

        button[onclick*="editRate"] {
            background: var(--light-blue);
            color: var(--primary-blue);
        }

        button[onclick*="editRate"]:hover {
            background: var(--primary-blue);
            color: white;
        }

        button[onclick*="deleteRate"] {
            background: #FEE2E2;
            color: #DC2626;
        }

        button[onclick*="deleteRate"]:hover {
            background: #DC2626;
            color: white;
        }

        /* Success Message Styles */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .message.success {
            background-color: #4CAF50;
        }

        .message.error {
            background-color: #DC2626;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }

        @media (max-width: 1024px) {
            .container {
                padding: 1rem;
            }

            form {
                padding: 1.5rem;
            }

            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            td, th {
                min-width: 120px;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .button-group {
                justify-content: center;
            }
        }

        .weight-rate {
            transition: all 0.3s ease;
        }
        
        .weight-rate.express-premium {
            display: block;
        }

        /* Updated Filter Section Styles */
        .filter-section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .filter-group {
            display: flex;
            gap: 15px;
            flex: 1;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 250px;
            background-color: white;
            cursor: pointer;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .filter-button {
            background: var(--primary-blue);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .filter-button:hover {
            background: var(--hover-blue);
        }

        .reset-button {
            background: #f3f4f6;
            color: #4b5563;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .reset-button:hover {
            background: #e5e7eb;
        }

        @media (max-width: 768px) {
            .filter-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                flex-direction: column;
            }
            
            .filter-input {
                width: 100%;
            }
        }

        /* Global Filter Section Styles */
        .global-filter-section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .global-filter-section .filter-group {
            display: flex;
            gap: 15px;
            flex: 1;
        }

        .global-filter-section .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 300px;
            background-color: white;
            cursor: pointer;
        }

        .global-filter-section .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .global-filter-section .filter-button {
            background: var(--primary-blue);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .global-filter-section .filter-button:hover {
            background: var(--hover-blue);
        }

        .global-filter-section .reset-button {
            background: #f3f4f6;
            color: #4b5563;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .global-filter-section .reset-button:hover {
            background: #e5e7eb;
        }

        @media (max-width: 768px) {
            .global-filter-section {
                flex-direction: column;
                align-items: stretch;
                position: static;
            }
            
            .global-filter-section .filter-group {
                flex-direction: column;
            }
            
            .global-filter-section .filter-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>

<?php if (isset($_SESSION['rate_message'])): ?>
    <?php 
    $messageType = (strpos(strtolower($_SESSION['rate_message']), 'successfully') !== false) ? 'success' : 'error';
    ?>
    <div class="message <?php echo $messageType; ?>" id="successMessage">
        <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i> 
        <?php echo htmlspecialchars($_SESSION['rate_message']); ?>
    </div>
    <?php unset($_SESSION['rate_message']); ?>
<?php endif; ?>

<div class="container">
    <h2>Rate Master</h2>

    <form action="index.php?page=process_rate" method="POST">
        <input type="hidden" name="id" value="<?php echo $rate['id']; ?>">
        
        <div class="form-grid">
            <div class="form-group">
                <label>Customer:</label>
                <select name="short_name" required>
                    <option value="">Select Customer</option>
                    <?php
                    $cust_sql = "SELECT short_name FROM customers WHERE username = ?";
                    $cust_stmt = $conn->prepare($cust_sql);
                    $cust_stmt->bind_param("s", $username);
                    $cust_stmt->execute();
                    $cust_result = $cust_stmt->get_result();
                    while ($row = $cust_result->fetch_assoc()) {
                        $selected = ($rate['short_name'] == $row['short_name']) ? "selected" : "";
                        echo "<option value='" . $row['short_name'] . "' $selected>" . $row['short_name'] . "</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="form-group">
                <label>Mode:</label>
                <select name="mode_of_tsp" id="mode_of_tsp" required onchange="handleModeChange()">
                    <option value="">Select Mode</option>
                    <option value="Express" <?php echo ($rate['mode_of_tsp'] == 'Express') ? 'selected' : ''; ?>>Express</option>
                    <option value="Surface" <?php echo ($rate['mode_of_tsp'] == 'Surface') ? 'selected' : ''; ?>>Surface</option>
                    <option value="Premium" <?php echo ($rate['mode_of_tsp'] == 'Premium') ? 'selected' : ''; ?>>Premium</option>
                </select>
            </div>

            <div class="form-group">
                <label>Zone:</label>
                <select name="zone" id="zone_select" required>
                    <option value="">Select Zone</option>
                    <?php 
                    $zones = ['Metro', 'ROI', 'Zonal', 'SPLDST', 'Intra'];
                    foreach ($zones as $zone) {
                        $selected = ($rate['zone'] == $zone) ? "selected" : "";
                        echo "<option value='$zone' $selected>$zone</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="form-group weight-rate express-premium">
                <label>Up to 250gms:</label>
                <input type="number" step="0.01" name="up_to_0250" value="<?php echo $rate['up_to_0250']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate express-premium">
                <label>Up to 500gms:</label>
                <input type="number" step="0.01" name="up_to_0500" value="<?php echo $rate['up_to_0500']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate express-premium">
                <label>Additional 500gms:</label>
                <input type="number" step="0.01" name="addl_500gm" value="<?php echo $rate['addl_500gm']; ?>" placeholder="Enter rate">
            </div>

            <div class="form-group weight-rate surface-mode" style="display: none;">
                <label>1 Kg to 10 Kg Rate (per kg):</label>
                <input type="number" step="0.01" name="up_to_0250" value="<?php echo ($rate['mode_of_tsp'] == 'Surface' ? $rate['up_to_0250'] : ''); ?>" placeholder="Enter per kg rate">
            </div>

            <div class="form-group weight-rate surface-mode" style="display: none;">
                <label>10 Kg to 20 Kg Rate (per kg):</label>
                <input type="number" step="0.01" name="up_to_0500" value="<?php echo ($rate['mode_of_tsp'] == 'Surface' ? $rate['up_to_0500'] : ''); ?>" placeholder="Enter per kg rate">
            </div>

            <div class="form-group weight-rate surface-mode" style="display: none;">
                <label>Above 20 Kg Rate (per kg):</label>
                <input type="number" step="0.01" name="addl_500gm" value="<?php echo ($rate['mode_of_tsp'] == 'Surface' ? $rate['addl_500gm'] : ''); ?>" placeholder="Enter per kg rate">
            </div>

            <div class="form-group weight-rate surface-mode" style="display: none;">
                <label>Minimum Chargeable Weight (kg):</label>
                <input type="number" step="0.01" name="min_weight" value="<?php echo ($rate['mode_of_tsp'] == 'Surface' ? $rate['above_3kg'] : ''); ?>" placeholder="Enter minimum weight">
            </div>

            <div class="form-group weight-rate express-premium">
                <label id="per_kg_label">Above 3kg:</label>
                <input type="number" step="0.01" name="above_3kg" value="<?php echo ($rate['mode_of_tsp'] != 'Surface' ? $rate['above_3kg'] : ''); ?>" placeholder="Enter rate">
            </div>

            <div class="button-group">
                <button type="submit">
                    <?php echo $editMode ? "Update Rate" : "Save Rate"; ?>
                </button>
            </div>
        </div>
    </form>

    <hr>

    <div class="global-filter-section">
        <div class="filter-group">
            <select id="globalCustomerFilter" class="filter-input">
                <option value="">All Customers</option>
                <?php
                $cust_sql = "SELECT DISTINCT short_name FROM rate_master WHERE username = ? ORDER BY short_name";
                $cust_stmt = $conn->prepare($cust_sql);
                $cust_stmt->bind_param("s", $username);
                $cust_stmt->execute();
                $cust_result = $cust_stmt->get_result();
                while ($row = $cust_result->fetch_assoc()) {
                    echo "<option value='" . $row['short_name'] . "'>" . $row['short_name'] . "</option>";
                }
                ?>
            </select>
        </div>
        
        <button onclick="resetAllFilters()" class="reset-button">Reset</button>
    </div>

    <h3>Express Rate List</h3>
    <table id="express-table">
        <thead>
        <tr>
            <th>Customer</th>
            <th>Mode</th>
            <th>Zone</th>
            <th>Up to 250gms</th>
            <th>Up to 500gms</th>
            <th>Addl 500gms</th>
            <th>Above 3kg</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Express') { ?>
            <tr>
                <td><?php echo $row['short_name']; ?></td>
                <td><?php echo $row['mode_of_tsp']; ?></td>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['up_to_0250']; ?></td>
                <td><?php echo $row['up_to_0500']; ?></td>
                <td><?php echo $row['addl_500gm']; ?></td>
                <td><?php echo $row['above_3kg']; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>

    <h3 style="margin-top: 2rem;">Premium Rate List</h3>
    <table id="premium-table">
        <thead>
        <tr>
            <th>Customer</th>
            <th>Mode</th>
            <th>Zone</th>
            <th>Up to 250gms</th>
            <th>Up to 500gms</th>
            <th>Addl 500gms</th>
            <th>Above 10kg</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Premium') { ?>
            <tr>
                <td><?php echo $row['short_name']; ?></td>
                <td><?php echo $row['mode_of_tsp']; ?></td>
                <td><?php echo $row['pr_zone']; ?></td>
                <td><?php echo $row['up_to_0250']; ?></td>
                <td><?php echo $row['up_to_0500']; ?></td>
                <td><?php echo $row['addl_500gm']; ?></td>
                <td><?php echo $row['above_3kg']; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>

    <h3 style="margin-top: 2rem;">Surface Rate List</h3>
    <table id="surface-table">
        <thead>
        <tr>
            <th>Customer</th>
            <th>Mode</th>
            <th>Zone</th>
            <th>1-10 Kg Rate</th>
            <th>10-20 Kg Rate</th>
            <th>Above 20 Kg Rate</th>
            <th>Min. Weight (kg)</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php 
        $result->data_seek(0);
        while ($row = $result->fetch_assoc()) { 
            if ($row['mode_of_tsp'] == 'Surface') { ?>
            <tr>
                <td><?php echo $row['short_name']; ?></td>
                <td><?php echo $row['mode_of_tsp']; ?></td>
                <td><?php echo $row['zone']; ?></td>
                <td><?php echo $row['up_to_0250']; ?></td>
                <td><?php echo $row['up_to_0500']; ?></td>
                <td><?php echo $row['addl_500gm']; ?></td>
                <td><?php echo $row['above_3kg']; ?></td>
                <td>
                    <button onclick="editRate(<?php echo $row['id']; ?>)">✏️ Edit</button>
                    <button onclick="deleteRate(<?php echo $row['id']; ?>)">❌ Delete</button>
                </td>
            </tr>
        <?php } } ?>
        </tbody>
    </table>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const successMessage = document.getElementById('successMessage');
    if (successMessage) {
        successMessage.classList.add('show-message');
        setTimeout(() => {
            successMessage.classList.remove('show-message');
            setTimeout(() => {
                successMessage.style.display = 'none';
            }, 500);
        }, 3000);
    }

    // Initialize form based on selected mode
    handleModeChange();
    updateZoneOptions();
});

function handleModeChange() {
    const modeSelect = document.getElementById('mode_of_tsp');
    const expressFields = document.querySelectorAll('.weight-rate.express-premium');
    const surfaceFields = document.querySelectorAll('.weight-rate.surface-mode');
    const perKgLabel = document.getElementById('per_kg_label');
    
    updateZoneOptions();
    
    if (modeSelect.value === 'Surface') {
        // Hide Express/Premium fields and show Surface fields
        expressFields.forEach(field => {
            field.style.display = 'none';
            const input = field.querySelector('input');
            if (input) {
                input.required = false;
                input.disabled = true;
            }
        });
        surfaceFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
    } else if (modeSelect.value === 'Express') {
        // Show Express fields and hide Surface fields
        expressFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
        surfaceFields.forEach(field => {
            field.style.display = 'none';
            const input = field.querySelector('input');
            if (input) {
                input.required = false;
                input.disabled = true;
            }
        });
        perKgLabel.textContent = 'Above 3kg:';
    } else if (modeSelect.value === 'Premium') {
        // Show Premium fields and hide Surface fields
        expressFields.forEach(field => {
            field.style.display = 'block';
            const input = field.querySelector('input');
            if (input) {
                input.required = true;
                input.disabled = false;
            }
        });
        surfaceFields.forEach(field => {
            field.style.display = 'none';
            const input = field.querySelector('input');
            if (input) {
                input.required = false;
                input.disabled = true;
            }
        });
        perKgLabel.textContent = 'Above 10kg:';
    }
}

function updateZoneOptions() {
    const modeSelect = document.getElementById('mode_of_tsp');
    const zoneSelect = document.getElementById('zone_select');
    const currentValue = zoneSelect.value;
    
    zoneSelect.innerHTML = '<option value="">Select Zone</option>';
    
    if (modeSelect.value === 'Premium') {
        const premiumZones = ['Intra', 'Metro', 'ROI', 'Regional', 'Zonal'];
        premiumZones.forEach(zone => {
            const option = document.createElement('option');
            option.value = zone;
            option.textContent = zone;
            zoneSelect.appendChild(option);
        });
    } else {
        const defaultZones = ['Metro', 'ROI', 'Intra', 'SPLDST', 'MGG'];
        defaultZones.forEach(zone => {
            const option = document.createElement('option');
            option.value = zone;
            option.textContent = zone;
            zoneSelect.appendChild(option);
        });
    }
    
    if (currentValue) {
        const options = Array.from(zoneSelect.options);
        const exists = options.some(option => option.value === currentValue);
        if (exists) {
            zoneSelect.value = currentValue;
        }
    }
}

function editRate(id) {
    window.location.href = 'index.php?page=rate_master&id=' + id;
}

function deleteRate(id) {
    if (confirm("Are you sure to delete this Rate?")) {
        window.location.href = 'index.php?page=delete_rate&id=' + id;
    }
}

function filterAllTables() {
    const customerFilter = document.getElementById('globalCustomerFilter').value;
    const tables = ['express-table', 'premium-table', 'surface-table'];
    
    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');
        
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const customer = row.cells[0].textContent;
            
            if (!customerFilter) {
                row.style.display = '';
            } else {
                row.style.display = customer === customerFilter ? '' : 'none';
            }
        }
    });
}

function resetAllFilters() {
    const tables = ['express-table', 'premium-table', 'surface-table'];
    
    document.getElementById('globalCustomerFilter').value = '';
    
    tables.forEach(tableId => {
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            rows[i].style.display = '';
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const customerFilter = document.getElementById('globalCustomerFilter');
    if (customerFilter) {
        customerFilter.addEventListener('change', filterAllTables);
    }
});
</script>

</body>
</html>
