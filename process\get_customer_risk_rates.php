<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
include '../db_connect.php';

// Check if customer parameter is provided
if (!isset($_GET['customer'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Customer parameter is required'
    ]);
    exit;
}

$customer = $_GET['customer'];

// Prepare and execute query to get customer's risk rates
$query = "SELECT owner_risk, carrier_risk FROM customers WHERE short_name = ? AND username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $customer, $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    // Return the risk rates
    echo json_encode([
        'success' => true,
        'owner_risk' => floatval($row['owner_risk']),
        'carrier_risk' => floatval($row['carrier_risk'])
    ]);
} else {
    // Customer not found
    echo json_encode([
        'success' => false,
        'error' => 'Customer not found'
    ]);
}

// Close statement and connection
$stmt->close();
$conn->close();
?> 