<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get values from form
    $mobile = trim($_POST['mobile']);
    $template = trim($_POST['template']);
    $params = trim($_POST['params']);
    $type = $_POST['type'];
    $url = trim($_POST['url']);

    // Credentials
    $user = "Nagorao_7";
    $pass = "123456"; // Replace with actual password
    $sender = "BUZWAP";
    $priority = "wa";
    $stype = "normal";

    // API endpoint
    $api_url = "http://bhashsms.com/api/sendmsg.php";

    // Prepare query
    $query = [
        "user" => $user,
        "pass" => $pass,
        "sender" => $sender,
        "phone" => $mobile,
        "text" => $template,
        "priority" => $priority,
        "stype" => $stype
    ];

    if (!empty($params)) {
        $query["Params"] = $params;
    }

    if ($type !== "text" && !empty($url)) {
        $query["htype"] = $type;
        $query["url"] = $url;
    }

    $final_url = $api_url . "?" . http_build_query($query);

    // Send request
    $response = file_get_contents($final_url);

    echo "<h3>Response:</h3>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
} else {
    echo "Invalid request.";
}
?>
