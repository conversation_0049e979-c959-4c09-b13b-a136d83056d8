<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ALM Cache Monitor</title>
    <script src="../js/cache-manager.js"></script>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2196F3;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2196F3;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .cache-details {
            margin-top: 30px;
        }
        .cache-item {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2196F3;
        }
        .cache-item.expired {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .cache-name {
            font-weight: bold;
            color: #333;
        }
        .cache-info {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .actions {
            margin: 20px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #1976D2;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ALM Cache Monitor</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalCaches">0</div>
                <div class="stat-label">Total Caches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="validCaches">0</div>
                <div class="stat-label">Valid Caches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="expiredCaches">0</div>
                <div class="stat-label">Expired Caches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSize">0 KB</div>
                <div class="stat-label">Total Size</div>
            </div>
        </div>

        <div class="actions">
            <button onclick="refreshStats()">Refresh Stats</button>
            <button onclick="cleanExpired()">Clean Expired</button>
            <button onclick="clearAllCaches()" class="danger">Clear All Caches</button>
        </div>

        <div id="status" class="status"></div>

        <div class="cache-details">
            <h2>Cache Details</h2>
            <div id="cacheList"></div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function formatBytes(bytes) {
            return window.CacheManager.getHumanReadableSize(bytes);
        }

        function formatAge(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) return `${hours}h ${minutes % 60}m`;
            if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
            return `${seconds}s`;
        }

        function refreshStats() {
            const stats = window.CacheManager.getStats();
            
            document.getElementById('totalCaches').textContent = stats.totalCaches;
            document.getElementById('validCaches').textContent = stats.validCaches;
            document.getElementById('expiredCaches').textContent = stats.expiredCaches;
            document.getElementById('totalSize').textContent = formatBytes(stats.totalSize);
            
            const cacheList = document.getElementById('cacheList');
            cacheList.innerHTML = '';
            
            if (Object.keys(stats.cacheDetails).length === 0) {
                cacheList.innerHTML = '<p>No caches found</p>';
                return;
            }
            
            Object.entries(stats.cacheDetails).forEach(([name, details]) => {
                const div = document.createElement('div');
                div.className = `cache-item ${details.isValid ? '' : 'expired'}`;
                div.innerHTML = `
                    <div class="cache-name">${name}</div>
                    <div class="cache-info">
                        Size: ${formatBytes(details.size)} | 
                        Age: ${formatAge(details.age)} | 
                        Status: ${details.isValid ? 'Valid' : 'Expired'} |
                        Key: ${details.key}
                    </div>
                `;
                cacheList.appendChild(div);
            });
            
            showStatus('Stats refreshed successfully');
        }

        function cleanExpired() {
            const cleaned = window.CacheManager.cleanExpired();
            showStatus(`Cleaned ${cleaned} expired cache entries`);
            refreshStats();
        }

        function clearAllCaches() {
            if (confirm('Are you sure you want to clear all caches? This will force all pages to reload data from the server.')) {
                window.CacheManager.clearAll();
                showStatus('All caches cleared successfully');
                refreshStats();
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(refreshStats, 30000);

        // Initial load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(refreshStats, 500); // Wait for cache manager to initialize
        });
    </script>
</body>
</html>
