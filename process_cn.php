<?php 
session_start();

// Ensure user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in to create CN entries.'); window.location.href='login.php';</script>");
}

// Database Connection for InfinityFree
$servername = "localhost";
$username = "u111133901_astra_logistic";
$password = "<PERSON><PERSON><PERSON>@0505";
$database = "u111133901_astra_logistic";
// Connect to MySQL
$conn = new mysqli($servername, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get form data
$start_serial = isset($_POST['start_serial']) ? trim($_POST['start_serial']) : "";
$end_serial = isset($_POST['end_serial']) ? trim($_POST['end_serial']) : "";
$cn_date = isset($_POST['cn_date']) ? $_POST['cn_date'] : "";
$cn_type = isset($_POST['cn_type']) ? $_POST['cn_type'] : "";
$logged_in_username = $_SESSION['username'];

// Extract Prefix and Serial Numbers
$prefix = substr($start_serial, 0, 1);
$start_serial_num = intval(substr($start_serial, 1)); 
$end_serial_num = intval(substr($end_serial, 1));

// Validate Serial Range
if ($start_serial_num > $end_serial_num) {
    die("<script>alert('Error: Start Serial must be less than End Serial.'); window.history.back();</script>");
}

// Prepare SQL Statement to Insert Data
$stmt = $conn->prepare("INSERT INTO cn_entries (cn_number, cn_date, cn_expiry_date, cn_type, username) VALUES (?, ?, ?, ?, ?)");
$stmt->bind_param("sssss", $cn_number, $cn_date, $cn_expiry_date, $cn_type, $logged_in_username);

// Loop through and insert all CN Numbers
for ($i = $start_serial_num; $i <= $end_serial_num; $i++) {
    $cn_number = $prefix . str_pad($i, 8, '0', STR_PAD_LEFT); 
    $cn_expiry_date = date('Y-m-d', strtotime($cn_date . ' + 180 days'));

    $stmt->execute();
}

// Close the statement & connection
$stmt->close();
$conn->close();

// Show message box and redirect
echo "<script>alert('✅ CN Entries successfully saved!'); window.location.href = 'https://astradigitalsolutions.in/index.php?page=cn-entry';</script>";
exit();
?>
