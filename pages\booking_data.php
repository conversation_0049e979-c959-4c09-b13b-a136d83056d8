<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start output buffering to catch any errors
ob_start();

// Only start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['excel_file'])) {
    $debug_output = [];
    $debug_output[] = "File upload process started";
    
    try {
        // Fix the vendor autoload path
        $vendor_path = __DIR__ . '/../vendor/autoload.php';
        if (!file_exists($vendor_path)) {
            throw new Exception('Composer autoload file not found at: ' . $vendor_path);
        }
        
        require $vendor_path;
        $debug_output[] = "Autoload file included successfully";

        $username = $_SESSION['username'];
        $file = $_FILES['excel_file'];
        $debug_output[] = "File details: " . print_r($file, true);

        // Check if file upload was successful
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $upload_errors = [
                UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
                UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
                UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
            ];
            $error_message = isset($upload_errors[$file['error']]) ? $upload_errors[$file['error']] : 'Unknown upload error';
            throw new Exception('File upload error: ' . $error_message);
        }

        // Validate file
        $allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        $debug_output[] = "File type: " . $file['type'];
        if (!in_array($file['type'], $allowed_types)) {
            $debug_output[] = "Invalid file type detected: " . $file['type'];
            throw new Exception('Invalid file type. Please upload an Excel file (.xlsx or .xls)');
        }

        // Read Excel file
        $debug_output[] = "Attempting to load Excel file";
        try {
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            $debug_output[] = "Excel file loaded successfully. Number of rows: " . count($rows);
        } catch (Exception $e) {
            throw new Exception('Error reading Excel file: ' . $e->getMessage());
        }

        // Remove header row
        array_shift($rows);

        // Prepare insert statement
        $stmt = $conn->prepare("INSERT INTO booking_data (DSR_CNNO, DSR_BOOKING_DATE, DSR_DEST_PINCODE, DSR_CN_WEIGHT, DSR_MODE, DSR_CN_TYPE, username, created_at, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)");

        // Prepare statement to check docket_no, pincode, weight and mode_of_tsp
        $check_stmt = $conn->prepare("SELECT docket_no, pincode, weight, mode_of_tsp FROM transactions WHERE docket_no = ? AND username = ?");

        // Prepare statement to check for duplicate DSR_CNNO in booking_data
        $duplicate_check_stmt = $conn->prepare("SELECT DSR_CNNO FROM booking_data WHERE DSR_CNNO = ? AND username = ?");

        // Prepare statement to check if docket exists in transactions
        $docket_check_stmt = $conn->prepare("SELECT docket_no FROM transactions WHERE docket_no = ? AND username = ?");

        $successCount = 0;
        $errors = [];
        $duplicates = [];

        foreach ($rows as $index => $row) {
            if (empty($row[1])) {
                $debug_output[] = "Skipping empty row at index: " . $index;
                continue;
            }

            try {
                $debug_output[] = "\n=== Processing Row " . ($index + 2) . " ===";
                $debug_output[] = "Raw row data: " . print_r($row, true);
                
                // New column mapping
                $dsr_cnno = trim($row[1]); // B
                $dsr_cn_weight = floatval(trim($row[4])); // E
                $dsr_cn_type = trim($row[5]); // F
                $dsr_mode = trim($row[7]); // H
                $dest_pincode = trim($row[9]); // J
                $booking_date = date('Y-m-d', strtotime(trim($row[10]))); // K

                $debug_output[] = "Processed values:";
                $debug_output[] = "- DSR_CNNO: $dsr_cnno";
                $debug_output[] = "- Weight: $dsr_cn_weight";
                $debug_output[] = "- Type: $dsr_cn_type";
                $debug_output[] = "- Mode: $dsr_mode";
                $debug_output[] = "- Pincode: $dest_pincode";
                $debug_output[] = "- Booking Date: $booking_date";

                // Use the logged-in user's username instead of the one from Excel
                $username = $_SESSION['username'];
                $debug_output[] = "Using username: $username";

                // Initialize remarks array
                $remarks = [];

                // First check if docket exists in transactions table
                $docket_check_stmt->bind_param("ss", $dsr_cnno, $username);
                $docket_check_stmt->execute();
                $docket_result = $docket_check_stmt->get_result();
                
                if ($docket_result->num_rows === 0) {
                    $debug_output[] = "WARNING: Docket not found in transactions table";
                    $remarks[] = 'Docket entry missing';
                } else {
                    $debug_output[] = "Docket found in transactions table";
                }

                // Check for duplicate DSR_CNNO in booking_data
                $duplicate_check_stmt->bind_param("ss", $dsr_cnno, $username);
                $duplicate_check_stmt->execute();
                $duplicate_result = $duplicate_check_stmt->get_result();
                
                $debug_output[] = "Duplicate check details:";
                $debug_output[] = "- Checking DSR_CNNO: '$dsr_cnno'";
                $debug_output[] = "- Username: '$username'";
                $debug_output[] = "- SQL Query: SELECT DSR_CNNO FROM booking_data WHERE DSR_CNNO = '$dsr_cnno' AND username = '$username'";
                $debug_output[] = "- Found matches: " . $duplicate_result->num_rows;
                
                if ($duplicate_result->num_rows > 0) {
                    $debug_output[] = "ERROR: Duplicate docket found in booking_data";
                    $duplicates[] = "Row " . ($index + 2) . ": Docket number '$dsr_cnno' already exists in booking data";
                    continue;
                } else {
                    $debug_output[] = "No duplicate found in booking_data";
                }

                // Basic validation - only check if pincode is numeric
                if (!is_numeric($dest_pincode)) {
                    $debug_output[] = "WARNING: Invalid pincode format: $dest_pincode";
                    $remarks[] = "Invalid pincode format";
                }

                // Check if docket_no exists in transactions table and validate pincode, weight and mode
                $check_stmt->bind_param("ss", $dsr_cnno, $username);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $check_row = $check_result->fetch_assoc();

                if ($check_row) {
                    $debug_output[] = "Validating against transactions data:";
                    $debug_output[] = "- Transaction pincode: " . $check_row['pincode'];
                    $debug_output[] = "- Transaction weight: " . $check_row['weight'];
                    $debug_output[] = "- Transaction mode: " . $check_row['mode_of_tsp'];

                    if ($check_row['pincode'] != $dest_pincode) {
                        $debug_output[] = "WARNING: Pincode mismatch";
                        $remarks[] = 'Destination pincode mismatch';
                    }
                    if ($check_row['weight'] != $dsr_cn_weight) {
                        if ($check_row['weight'] < $dsr_cn_weight) {
                            $debug_output[] = "WARNING: Negative weight difference";
                            $remarks[] = 'Negative weight';
                        } else {
                            $debug_output[] = "WARNING: Positive weight difference";
                            $remarks[] = 'Positive weight';
                        }
                    }
                    // Check mode validation for all modes
                    if ($check_row['mode_of_tsp'] == 'Premium' && in_array($dsr_cn_type, ['AR1', 'AC1', 'SF1'])) {
                        $debug_output[] = "WARNING: Mode mismatch for Premium";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Express' && $dsr_cn_type != 'AR1') {
                        $debug_output[] = "WARNING: Mode mismatch for Express";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Surface' && $dsr_cn_type != 'SF1') {
                        $debug_output[] = "WARNING: Mode mismatch for Surface";
                        $remarks[] = 'Mode mismatch';
                    } else if ($check_row['mode_of_tsp'] == 'Air Cargo' && $dsr_cn_type != 'AC1') {
                        $debug_output[] = "WARNING: Mode mismatch for Air Cargo";
                        $remarks[] = 'Mode mismatch';
                    }
                }

                // Join remarks with comma if multiple exist
                $remarks_text = implode(', ', $remarks);
                $debug_output[] = "Final remarks: " . ($remarks_text ?: 'None');

                // Bind parameters and execute
                $stmt->bind_param("sssdssss",
                    $dsr_cnno,
                    $booking_date,
                    $dest_pincode,
                    $dsr_cn_weight,
                    $dsr_mode,
                    $dsr_cn_type,
                    $username,
                    $remarks_text
                );

                if ($stmt->execute()) {
                    $successCount++;
                    $debug_output[] = "SUCCESS: Row inserted successfully";
                } else {
                    $debug_output[] = "ERROR: Failed to insert row - " . $stmt->error;
                    $errors[] = "Error on row " . ($index + 2) . ": " . $stmt->error;
                }
            } catch (Exception $e) {
                $debug_output[] = "CRITICAL ERROR in row " . ($index + 2) . ": " . $e->getMessage();
                $errors[] = "Error on row " . ($index + 2) . ": " . $e->getMessage();
            }
        }

        $debug_output[] = "\n=== Import Summary ===";
        $debug_output[] = "Total rows processed: " . count($rows);
        $debug_output[] = "Successfully imported: " . $successCount;
        $debug_output[] = "Errors encountered: " . count($errors);
        $debug_output[] = "Duplicates found: " . count($duplicates);

        if ($successCount > 0) {
            $_SESSION['upload_message'] = "Successfully imported $successCount records!";
            if (!empty($errors)) {
                $_SESSION['upload_errors'] = $errors;
            }
            if (!empty($duplicates)) {
                $_SESSION['upload_duplicates'] = $duplicates;
            }
        } else {
            if (!empty($duplicates)) {
                // If all records were duplicates, show a more specific message
                $_SESSION['upload_error'] = "All records were duplicates. Please check if these records have already been imported.";
            } else {
                $_SESSION['upload_error'] = "No records were imported. Please check your file format.";
            }
        }

        // Store summary information in session
        $_SESSION['upload_summary'] = [
            'total_rows' => count($rows),
            'success_count' => $successCount,
            'error_count' => count($errors),
            'duplicate_count' => count($duplicates)
        ];

    } catch (Exception $e) {
        $debug_output[] = "Critical error in upload process: " . $e->getMessage();
        $_SESSION['upload_error'] = $e->getMessage();
    }

    // Store debug output in session
    $_SESSION['debug_output'] = $debug_output;
    
    // Get any output that might have been generated
    $output = ob_get_clean();
    if (!empty($output)) {
        $_SESSION['debug_output'][] = "Additional output: " . $output;
    }
    
    header("Location: index.php?page=booking_data");
    exit();
}

// Initial data load
$username = $_SESSION['username'];

// Get filter values
$current_month = date('m-Y');
$selected_month = $_GET['month'] ?? $current_month;

// Generate last 12 months for dropdown
$months = [];
for ($i = 0; $i < 12; $i++) {
    $date = strtotime("-$i months");
    $month_num = date('m', $date);
    $year = date('Y', $date);
    $month_name = date('M', $date);
    $months[] = [
        'value' => $month_num . '-' . $year,
        'label' => $month_name . '-' . $year
    ];
}

// Build the query with month filter
$sql = "SELECT * FROM booking_data WHERE username = ?";
$params = [$username];
$types = "s";

// Always filter by month (current month by default)
$month_year = explode('-', $selected_month);
$sql .= " AND MONTH(DSR_BOOKING_DATE) = ? AND YEAR(DSR_BOOKING_DATE) = ?";
$params[] = $month_year[0];
$params[] = $month_year[1];
$types .= "ii";

$sql .= " ORDER BY created_at DESC";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Cache busting: <?php echo time(); ?> - FORCED REFRESH -->
    <style>
        /* Critical styles */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Success Message Styles */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }

        /* Add these styles to your existing CSS */
        .table-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 2;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-left: 2.5rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .filter-box {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .filter-box label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        .filter-box select {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-right: 2.5rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
            appearance: none;
            cursor: pointer;
        }

        .table thead th {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .table thead th:after {
            content: '↕';
            position: absolute;
            right: 1rem;
            opacity: 0.3;
        }

        .table thead th.asc:after {
            content: '↑';
            opacity: 1;
        }

        .table thead th.desc:after {
            content: '↓';
            opacity: 1;
        }

        .no-results {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .table-container {
            overflow-x: auto;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .table tbody tr.success-row {
            background-color: #e8f5e9;
        }

        .table tbody tr.error-row {
            background-color: #ffebee;
        }

        .section-title {
            color: var(--primary-blue);
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0 0 0.75rem 0;
        }

        .filter-download-section {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .filter-download-section .filter-box {
            flex: 1;
            min-width: 200px;
        }

        .filter-download-section .btn {
            min-width: 200px;
        }

        /* Filter Styles */
        .filters-section {
            width: 100%;
            margin-bottom: 0.75rem;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            display: block !important;
            visibility: visible !important;
        }

        .filters-grid {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 180px;
        }

        .filter-group label {
            font-size: 0.85rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .filter-input {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: var(--background);
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .btn-apply, .btn-reset {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
        }

        .btn-apply {
            background: var(--primary-blue);
            color: white;
            border: none;
        }

        .btn-apply:hover {
            background: var(--hover-blue);
        }

        .btn-reset {
            background: var(--light-blue);
            color: var(--primary-blue);
            border: 1px solid transparent;
        }

        .btn-reset:hover {
            background: var(--primary-blue);
            color: white;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .booking-container {
            padding: 1rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            /* COMPACT LAYOUT ACTIVE - TEST */
            background: linear-gradient(45deg, #f8f9fa, #ffffff);
        }

        .header-section {
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            margin-bottom: 1rem;
            /* Compact layout applied */
            border-left: 3px solid var(--primary-blue);
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
        }

        .upload-section {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .file-upload-container {
            flex: 1;
            min-width: 280px;
        }

        .file-input-wrapper {
            position: relative;
            width: 100%;
        }

        .file-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
        }

        .btn-primary:hover {
            background: var(--hover-blue);
        }

        .btn-secondary {
            background: var(--light-blue);
            color: var(--primary-blue);
        }

        .btn-secondary:hover {
            background: var(--primary-blue);
            color: white;
        }

        .data-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            overflow: hidden;
            margin-top: 1rem;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        .table tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .booking-container {
                padding: 0.75rem;
            }

            .header-section {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .upload-section {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .filters-grid {
                flex-direction: column;
                gap: 0.75rem;
            }

            .filter-buttons {
                flex-direction: row;
                gap: 0.5rem;
            }

            .btn-apply, .btn-reset {
                flex: 1;
            }

            .filter-group {
                min-width: auto;
            }

            .table thead {
                display: none;
            }

            .table tbody td {
                display: block;
                padding: 0.5rem 1rem;
                text-align: right;
                border: none;
            }

            .table tbody tr {
                display: block;
                border-bottom: 1px solid var(--border-color);
                padding: 0.5rem 0;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: var(--primary-blue);
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <div class="booking-container">
            <?php if (isset($_SESSION['upload_message'])): ?>
                <div class="message success" id="successMessage">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_message']); ?>
                </div>
                <?php unset($_SESSION['upload_message']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['upload_error'])): ?>
                <div class="message error" id="errorMessage">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['upload_error']); ?>
                </div>
                <?php unset($_SESSION['upload_error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['upload_errors'])): ?>
                <div class="message warning" id="warningMessage">
                    <i class="fas fa-exclamation-triangle"></i> Some rows had errors:
                    <ul>
                        <?php foreach ($_SESSION['upload_errors'] as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php unset($_SESSION['upload_errors']); ?>
            <?php endif; ?>

            <div class="header-section">
                <h1 class="page-title">📊 Upload Booking Data</h1>
                <form method="POST" enctype="multipart/form-data" class="upload-section">
                    <div class="file-upload-container">
                        <div class="file-input-wrapper">
                            <input type="file"
                                   name="excel_file"
                                   class="file-input"
                                   accept=".xlsx,.xls"
                                   required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i>
                        Submit
                    </button>
                    <button type="button" id="refreshBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <a href="../upload/Booking_data_upload_template.xlsx" class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Download Excel Template
                    </a>
                </form>
            </div>

            <div class="header-section">
                <h2 class="section-title">Filters</h2>
                <div class="filters-section">
                    <form id="filterForm" method="GET" action="index.php" class="filters-grid">
                        <input type="hidden" name="page" value="booking_data">

                        <div class="filter-group">
                            <label for="month">Month</label>
                            <select id="month" name="month" class="filter-input">
                                <?php
                                // Debug: Check if months array exists
                                if (isset($months) && !empty($months)) {
                                    foreach ($months as $month): ?>
                                        <option value="<?php echo htmlspecialchars($month['value']); ?>" <?php echo $selected_month === $month['value'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($month['label']); ?>
                                        </option>
                                    <?php endforeach;
                                } else {
                                    // Fallback if months array is not set
                                    for ($i = 0; $i < 12; $i++) {
                                        $date = strtotime("-$i months");
                                        $month_num = date('m', $date);
                                        $year = date('Y', $date);
                                        $month_name = date('M', $date);
                                        $value = $month_num . '-' . $year;
                                        $label = $month_name . '-' . $year;
                                        $selected = ($selected_month === $value) ? 'selected' : '';
                                        echo "<option value=\"$value\" $selected>$label</option>";
                                    }
                                } ?>
                            </select>
                        </div>

                        <div class="filter-group filter-buttons">
                            <button type="submit" class="btn-apply">Apply Filters</button>
                            <a href="index.php?page=booking_data" class="btn-reset">Reset</a>
                        </div>
                    </form>
                </div>
            </div>

            <div class="header-section">
                <h2 class="section-title">Download Filtered Data</h2>
                <div class="filter-download-section">
                    <div class="filter-box">
                        <label for="downloadRemarksFilter">Filter remarks</label>
                        <select id="downloadRemarksFilter">
                            <option value="">All Remarks</option>
                            <option value="Docket entry missing">Docket entry missing</option>
                            <option value="Destination pincode mismatch">Destination pincode mismatch</option>
                            <option value="Negative weight">Negative weight</option>
                            <option value="Positive weight">Positive weight</option>
                            <option value="Mode mismatch">Mode mismatch</option>
                        </select>
                    </div>
                    <div class="filter-box">
                        <label for="downloadMonthFilter">Filter month</label>
                        <select id="downloadMonthFilter">
                            <?php foreach ($months as $month): ?>
                                <option value="<?php echo $month['value']; ?>" <?php echo $selected_month === $month['value'] ? 'selected' : ''; ?>>
                                    <?php echo $month['label']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="button" id="downloadBtn" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        Download Filtered Data
                    </button>
                </div>
            </div>

            <div class="data-table">
                <div class="table-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search in all columns...">
                    </div>
                    <div class="filter-box">
                        <label for="remarksFilter">Filter remarks</label>
                        <select id="remarksFilter">
                            <option value="">All Remarks</option>
                            <option value="Docket entry missing">Docket entry missing</option>
                            <option value="Destination pincode mismatch">Destination pincode mismatch</option>
                            <option value="Negative weight">Negative weight</option>
                            <option value="Positive weight">Positive weight</option>
                            <option value="Mode mismatch">Mode mismatch</option>
                        </select>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th data-sort="DSR_CNNO">DSR_CNNO</th>
                                <th data-sort="DSR_BOOKING_DATE">DSR_BOOKING_DATE</th>
                                <th data-sort="DSR_DEST_PINCODE">DSR_DEST_PINCODE</th>
                                <th data-sort="DSR_CN_WEIGHT">DSR_CN_WEIGHT</th>
                                <th data-sort="DSR_MODE">DSR_MODE</th>
                                <th data-sort="remarks">Remarks</th>
                            </tr>
                        </thead>
                        <tbody id="bookingTableBody">
                            <?php while ($row = $result->fetch_assoc()): ?>
                            <tr class="<?php echo empty($row['remarks']) ? 'success-row' : 'error-row'; ?>">
                                <td data-label="DSR_CNNO"><?php echo htmlspecialchars($row['DSR_CNNO']); ?></td>
                                <td data-label="DSR_BOOKING_DATE"><?php echo date('d-M-Y', strtotime($row['DSR_BOOKING_DATE'])); ?></td>
                                <td data-label="DSR_DEST_PINCODE"><?php echo htmlspecialchars($row['DSR_DEST_PINCODE']); ?></td>
                                <td data-label="DSR_CN_WEIGHT"><?php echo htmlspecialchars($row['DSR_CN_WEIGHT']); ?></td>
                                <td data-label="DSR_MODE"><?php echo htmlspecialchars($row['DSR_MODE']); ?></td>
                                <td data-label="Remarks"><?php echo empty($row['remarks']) ? 'Success' : htmlspecialchars($row['remarks']); ?></td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        let messageTimeout; // Variable to store message timeout
        let activeRequest = null; // Track active AJAX request
        let refreshInProgress = false; // Track refresh state

        // Function to show and handle message
        function showMessage(message, type) {
            // Clear any existing message timeout
            if (messageTimeout) {
                clearTimeout(messageTimeout);
            }

            // Remove any existing messages
            $('.message').remove();

            // Create and show new message
            const messageElement = $('<div class="message ' + type + '" id="' + type + 'Message">')
                .html('<i class="fas fa-' + (type === 'success' ? 'check-circle' : 'exclamation-circle') + '"></i> ' + message);
            $('.booking-container').prepend(messageElement);
            messageElement.addClass('show-message');

            // Set timeout to remove message after 2 seconds
            messageTimeout = setTimeout(() => {
                messageElement.removeClass('show-message');
                setTimeout(() => {
                    messageElement.remove();
                }, 500);
            }, 2000);
        }

        // Handle initial messages
        $('.message').each(function() {
            showMessage($(this).text().trim(), $(this).hasClass('success') ? 'success' : 'error');
        });

        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const refreshBtn = $('#refreshBtn');

        function showContent() {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.visibility = 'hidden';
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        // Function to handle loading state
        function setLoadingState(isLoading) {
            refreshInProgress = isLoading;
            refreshBtn.prop('disabled', isLoading);
            refreshBtn.css('opacity', isLoading ? '0.7' : '1');

            if (isLoading) {
                loadingOverlay.style.visibility = 'visible';
                loadingOverlay.style.opacity = '1';
                mainContent.style.visibility = 'hidden';
                mainContent.style.opacity = '0';
                document.body.style.overflow = 'hidden';
            } else {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.visibility = 'hidden';
                    mainContent.style.visibility = 'visible';
                    mainContent.style.opacity = '1';
                    document.body.style.overflow = 'auto';
                }, 300);
            }
        }

        // Add these new functions for filtering and sorting
        let currentSort = {
            column: null,
            direction: 'asc'
        };

        // Function to sort table
        function sortTable(column, direction) {
            const tbody = $('#bookingTableBody');
            const rows = tbody.find('tr').toArray().sort((a, b) => {
                const aValue = $(a).find(`td[data-label="${column}"]`).text().trim();
                const bValue = $(b).find(`td[data-label="${column}"]`).text().trim();

                // Handle date sorting
                if (column === 'DSR_BOOKING_DATE') {
                    return direction === 'asc'
                        ? new Date(aValue) - new Date(bValue)
                        : new Date(bValue) - new Date(aValue);
                }

                // Handle numeric sorting for weight
                if (column === 'DSR_CN_WEIGHT') {
                    return direction === 'asc'
                        ? parseFloat(aValue) - parseFloat(bValue)
                        : parseFloat(bValue) - parseFloat(aValue);
                }

                // Default string sorting
                return direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });

            // Clear and re-append sorted rows
            tbody.empty();
            rows.forEach(row => tbody.append(row));
        }

        // Function to filter table
        function filterTable(searchText, remarksFilter = '') {
            const tbody = $('#bookingTableBody');
            const rows = tbody.find('tr');
            let hasVisibleRows = false;

            rows.each(function() {
                const row = $(this);
                const text = row.text().toLowerCase();
                const remarks = row.find('td[data-label="Remarks"]').text().toLowerCase();

                // Check both search text and remarks filter
                const matchesSearch = text.includes(searchText.toLowerCase());
                const matchesRemarks = !remarksFilter || remarks.includes(remarksFilter.toLowerCase());

                row.toggle(matchesSearch && matchesRemarks);
                if (matchesSearch && matchesRemarks) hasVisibleRows = true;
            });

            // Show/hide no results message
            if (!hasVisibleRows) {
                if (!$('#noResults').length) {
                    tbody.append('<tr id="noResults"><td colspan="6" class="no-results">No matching records found</td></tr>');
                }
            } else {
                $('#noResults').remove();
            }
        }

        // Handle column sorting
        $('.table thead th').click(function() {
            const column = $(this).data('sort');

            // Update sort direction
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }

            // Update sort indicators
            $('.table thead th').removeClass('asc desc');
            $(this).addClass(currentSort.direction);

            // Sort table
            sortTable(column, currentSort.direction);
        });

        // Handle search input
        let searchTimeout;
        $('#searchInput').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterTable($(this).val(), $('#remarksFilter').val());
            }, 300);
        });

        // Handle remarks filter change
        $('#remarksFilter').on('change', function() {
            filterTable($('#searchInput').val(), $(this).val());
        });

        // Define the refreshData function
        function refreshData() {
            if (refreshInProgress) return;
            setLoadingState(true);

            if (activeRequest) {
                activeRequest.abort();
            }

            // Get current month filter from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const currentMonth = urlParams.get('month') || '<?php echo date('m-Y'); ?>';

            activeRequest = $.ajax({
                url: 'refresh_booking_data.php',
                method: 'POST',
                cache: false,
                data: {
                    _: new Date().getTime(),
                    month: currentMonth
                },
                success: function(response) {
                    try {
                        console.log('Response received:', response);
                        const data = JSON.parse(response);
                        if (data.success) {
                            const tbody = $('#bookingTableBody');
                            tbody.empty();

                            if (data.records && data.records.length > 0) {
                                data.records.forEach(function(record) {
                                    const row = `
                                        <tr class="${!record.remarks ? 'success-row' : 'error-row'}">
                                            <td data-label="DSR_CNNO">${record.DSR_CNNO}</td>
                                            <td data-label="DSR_BOOKING_DATE">${record.DSR_BOOKING_DATE}</td>
                                            <td data-label="DSR_DEST_PINCODE">${record.DSR_DEST_PINCODE}</td>
                                            <td data-label="DSR_CN_WEIGHT">${record.DSR_CN_WEIGHT}</td>
                                            <td data-label="DSR_MODE">${record.DSR_MODE}</td>
                                            <td data-label="Remarks">${!record.remarks ? 'Success' : record.remarks}</td>
                                        </tr>
                                    `;
                                    tbody.append(row);
                                });

                                // Reapply current sort if any
                                if (currentSort.column) {
                                    sortTable(currentSort.column, currentSort.direction);
                                }

                                // Reapply current filters
                                const searchText = $('#searchInput').val();
                                const remarksFilter = $('#remarksFilter').val();
                                filterTable(searchText, remarksFilter);

                                showMessage('Data refreshed successfully!', 'success');
                            } else {
                                showMessage('No records found', 'warning');
                            }
                        } else {
                            throw new Error(data.message || 'Failed to refresh data');
                        }
                    } catch (error) {
                        console.error('Error parsing response:', error);
                        showMessage('Error processing server response', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    if (status !== 'abort') {
                        console.error('AJAX error:', {xhr, status, error});
                        showMessage('Failed to refresh data. Please try again.', 'error');
                    }
                },
                complete: function() {
                    setLoadingState(false);
                    activeRequest = null;
                }
            });
        }

        // Handle Refresh button click
        refreshBtn.on('click', function(e) {
            e.preventDefault();
            refreshData();
        });

        // Handle Download button click
        $('#downloadBtn').on('click', function(e) {
            e.preventDefault();
            const remarksFilter = $('#downloadRemarksFilter').val();
            const monthFilter = $('#downloadMonthFilter').val();

            // Create a temporary form to submit the download request
            const form = $('<form>', {
                'method': 'POST',
                'action': 'download_booking_data.php'
            });

            // Add the filter values as hidden inputs
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'remarks_filter',
                'value': remarksFilter
            }));

            form.append($('<input>', {
                'type': 'hidden',
                'name': 'month_filter',
                'value': monthFilter
            }));

            // Append the form to the body and submit it
            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Prevent form submission on enter key
        $('form').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
            }
        });
    });
    </script>
</body>
</html>