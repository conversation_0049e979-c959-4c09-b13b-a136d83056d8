<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    $_SESSION['alert_message'] = "Error: You must be logged in.";
    $_SESSION['alert_type'] = 'error';
    header("Location: login.php");
    exit();
}

$username = $_SESSION['username'];
$search = isset($_GET['search']) ? $_GET['search'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Handle form submission for updating payment status
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_payment'])) {
    $id = $_POST['id'];
    $amount = $_POST['amount'];
    $payment_status = $_POST['payment_status'];
    $payment_received_date = $_POST['payment_received_date'];

    // Validate payment received date when status is Received
    if ($payment_status === 'Received' && empty($payment_received_date)) {
        $_SESSION['alert_message'] = "Payment received date is required when status is Received";
        $_SESSION['alert_type'] = 'error';
        
        $redirect_url = "index.php?page=pending_cash";
        if (!empty($search)) $redirect_url .= "&search=" . urlencode($search);
        if (!empty($start_date)) $redirect_url .= "&start_date=" . urlencode($start_date);
        if (!empty($end_date)) $redirect_url .= "&end_date=" . urlencode($end_date);
        
        echo "<script>window.location.href = '" . $redirect_url . "';</script>";
        exit();
    }

    $stmt = $conn->prepare("UPDATE transactions SET amount = ?, payment_status = ?, payment_received_date = ? WHERE id = ? AND username = ?");
    $stmt->bind_param("sssis", $amount, $payment_status, $payment_received_date, $id, $username);
    
    if ($stmt->execute()) {
        $_SESSION['alert_message'] = "Payment status updated successfully!";
        $_SESSION['alert_type'] = 'success';
    } else {
        $_SESSION['alert_message'] = "Error updating payment status: " . $stmt->error;
        $_SESSION['alert_type'] = 'error';
    }
    
    $redirect_url = "index.php?page=pending_cash";
    if (!empty($search)) $redirect_url .= "&search=" . urlencode($search);
    if (!empty($start_date)) $redirect_url .= "&start_date=" . urlencode($start_date);
    if (!empty($end_date)) $redirect_url .= "&end_date=" . urlencode($end_date);
    
    echo "<script>window.location.href = '" . $redirect_url . "';</script>";
    exit();
}

// Build the query with filters
$query = "SELECT * FROM transactions WHERE username = ? AND entry_type = 'cash' AND payment_status = 'Pending'";
$params = [$username];
$types = "s";

if (!empty($search)) {
    $query .= " AND docket_no LIKE ?";
    $params[] = "%$search%";
    $types .= "s";
}

if (!empty($start_date)) {
    $query .= " AND docket_date >= ?";
    $params[] = $start_date;
    $types .= "s";
}

if (!empty($end_date)) {
    $query .= " AND docket_date <= ?";
    $params[] = $end_date;
    $types .= "s";
}

$query .= " ORDER BY docket_date DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Critical styles that should load first */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden; /* Prevent scrolling during load */
        }

        /* Loading overlay - Critical */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Base critical styles for content */
        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        /* Success Message Styles */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            font-size: 16px;
            z-index: 1000;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .show-message {
            display: block;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        .header-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .search-section {
            flex: 1;
            max-width: 800px;
            min-width: 280px;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box, .date-input {
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
            transition: all 0.3s ease;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
        }

        .date-input {
            min-width: 150px;
        }

        .search-box:focus, 
        .date-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .btn-search, .clear-search {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-search {
            background: var(--primary-blue);
            color: white;
        }

        .btn-search:hover {
            background: var(--hover-blue);
        }

        .clear-search {
            background: var(--light-blue);
            color: var(--primary-blue);
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .table thead th:hover {
            background: var(--hover-blue);
        }

        .table thead th::after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.5;
        }

        .table thead th.sort-asc::after {
            content: '↑';
            opacity: 1;
        }

        .table thead th.sort-desc::after {
            content: '↓';
            opacity: 1;
        }

        .table tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .editable {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            padding: 0.5rem;
            border-radius: 4px;
            width: 100%;
            transition: all 0.2s ease;
        }

        .editable:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }

        .btn-save {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-save:hover {
            background: var(--hover-blue);
        }

        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-section {
                order: 2;
            }
            
            .search-group {
                flex-direction: column;
                width: 100%;
            }
            
            .search-box, 
            .date-input, 
            .btn-search, 
            .clear-search {
                width: 100%;
            }
            
            .table thead {
                display: none;
            }
            
            .table tbody td {
                display: block;
                padding: 0.5rem 1rem;
                text-align: right;
                border: none;
            }

            .table tbody tr {
                display: block;
                border-bottom: 1px solid var(--border-color);
                padding: 0.5rem 0;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: var(--primary-blue);
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <!-- Report Component -->
        <div class="report-container">
            <?php if (isset($_SESSION['alert_message'])): ?>
                <div class="message" id="successMessage">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['alert_message']); ?>
                </div>
                <?php unset($_SESSION['alert_message']); ?>
            <?php endif; ?>

            <div class="header-section">
                <h1 class="page-title">Pending Cash Payments</h1>
                <div class="search-section">
                    <div class="search-container">
                        <form method="GET" action="index.php" id="searchForm">
                            <input type="hidden" name="page" value="pending_cash">
                            <div class="search-group">
                                <input type="text" 
                                       id="searchBox" 
                                       name="search"
                                       class="search-box" 
                                       placeholder="Search by docket number..."
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       autocomplete="off">
                                <input type="date" 
                                       id="startDate" 
                                       name="start_date" 
                                       class="date-input" 
                                       value="<?php echo htmlspecialchars($start_date); ?>"
                                       placeholder="Start Date">
                                <input type="date" 
                                       id="endDate" 
                                       name="end_date" 
                                       class="date-input" 
                                       value="<?php echo htmlspecialchars($end_date); ?>"
                                       placeholder="End Date">
                                <button type="submit" class="btn-search">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <button type="button" id="clearSearch" class="clear-search">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="total-badge">
                    <i class="fas fa-file-alt"></i>
                    <span>Total Records: <span id="recordCount"><?php echo $result->num_rows; ?></span></span>
                </div>
            </div>

            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Docket No</th>
                            <th>Date</th>
                            <th>Pincode</th>
                            <th>Destination</th>
                            <th>Weight</th>
                            <th>Mode</th>
                            <th>Amount</th>
                            <th>Payment Status</th>
                            <th>Payment Date</th>
                            <th>Remarks</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td data-label="docket no"><?php echo htmlspecialchars($row['docket_no']); ?></td>
                            <td data-label="date"><?php echo date('d-M-Y', strtotime($row['docket_date'])); ?></td>
                            <td data-label="pincode"><?php echo htmlspecialchars($row['pincode']); ?></td>
                            <td data-label="destination"><?php echo htmlspecialchars($row['destination']); ?></td>
                            <td data-label="weight"><?php echo htmlspecialchars($row['weight']); ?> kg</td>
                            <td data-label="mode"><?php echo htmlspecialchars($row['mode_of_tsp']); ?></td>
                            <td data-label="amount">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                    <input type="hidden" name="update_payment" value="1">
                                    <input type="text" name="amount" value="<?php echo htmlspecialchars($row['amount']); ?>" class="editable" required>
                            </td>
                            <td data-label="payment status">
                                    <select name="payment_status" class="editable" required onchange="togglePaymentDate(this)">
                                        <option value="Pending" <?php echo ($row['payment_status'] == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                        <option value="Cash-Received" <?php echo ($row['payment_status'] == 'Cash-Received') ? 'selected' : ''; ?>>Cash-Received</option>
                                        <option value="Online-Received" <?php echo ($row['payment_status'] == 'Online-Received') ? 'selected' : ''; ?>>Online-Received</option>
                                    </select>
                            </td>
                            <td data-label="payment date">
                                    <input type="date" name="payment_received_date" 
                                           class="editable payment-date" 
                                           value="<?php echo htmlspecialchars($row['payment_received_date']); ?>"
                                           <?php echo ($row['payment_status'] == 'Received') ? 'required' : ''; ?>>
                            </td>
                            <td data-label="remarks"><?php echo htmlspecialchars($row['remarks']); ?></td>
                            <td data-label="actions">
                                    <button type="submit" class="btn-save" onclick="return validateForm(this)">
                                        <i class="fas fa-save"></i> Save
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        // Add success message handling
        const successMessage = document.getElementById('successMessage');
        if (successMessage) {
            successMessage.classList.add('show-message');
            setTimeout(() => {
                successMessage.classList.remove('show-message');
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 500);
            }, 3000);
        }

        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');
        
        // Ensure the loading overlay is visible initially
        loadingOverlay.style.visibility = 'visible';
        loadingOverlay.style.opacity = '1';
        
        // Hide main content initially
        mainContent.style.visibility = 'hidden';
        mainContent.style.opacity = '0';
        
        // Function to show content
        function showContent() {
            // First hide the loading overlay
            loadingOverlay.style.opacity = '0';
            
            // After a brief delay, show the main content
            setTimeout(() => {
                // Hide loading overlay completely
                loadingOverlay.style.visibility = 'hidden';
                
                // Show main content
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto'; // Re-enable scrolling
                
                // Show report container with a slight delay for smooth transition
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }
        
        // Wait for everything to load
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        const searchBox = $('#searchBox');
        const startDate = $('#startDate');
        const endDate = $('#endDate');
        const clearButton = $('#clearSearch');
        const searchForm = $('#searchForm');

        function updateClearButton() {
            const hasValue = searchBox.val().trim() !== '' || startDate.val() !== '' || endDate.val() !== '';
            clearButton.toggle(hasValue);
        }

        searchBox.on('input', updateClearButton);
        startDate.on('change', updateClearButton);
        endDate.on('change', updateClearButton);

        clearButton.on('click', function() {
            searchBox.val('');
            startDate.val('');
            endDate.val('');
            clearButton.hide();
            searchForm.submit();
        });

        updateClearButton();

        // Function to toggle payment date requirement
        function togglePaymentDate(selectElement) {
            const form = selectElement.closest('form');
            const dateInput = form.querySelector('input[name="payment_received_date"]');
            
            if (selectElement.value === 'Received') {
                dateInput.required = true;
                dateInput.style.borderColor = '#ffeeba';
                dateInput.style.backgroundColor = '#fff3cd';
            } else {
                dateInput.required = false;
                dateInput.style.borderColor = '#e0e0e0';
                dateInput.style.backgroundColor = '#F8FAFC';
            }
        }

        // Function to validate form before submission
        function validateForm(button) {
            const form = button.closest('form');
            const status = form.querySelector('select[name="payment_status"]').value;
            const dateInput = form.querySelector('input[name="payment_received_date"]');

            if (status === 'Received' && !dateInput.value) {
                alert('Please select a payment received date when status is Received');
                dateInput.focus();
                return false;
            }
            return true;
        }

        // Initialize payment date fields on page load
        document.querySelectorAll('select[name="payment_status"]').forEach(select => {
            togglePaymentDate(select);
        });
    });
    </script>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?> 