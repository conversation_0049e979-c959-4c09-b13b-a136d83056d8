<?php
session_start();
$successMessages = $_SESSION['successMessages'] ?? [];
$errorMessages = $_SESSION['errorMessages'] ?? [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload Results</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .message-list { max-width: 600px; margin: 50px auto; }
        .success-item { background: #d4edda; padding: 10px; margin-bottom: 5px; border-radius: 5px; }
        .error-item { background: #f8d7da; padding: 10px; margin-bottom: 5px; border-radius: 5px; }
        .close-button { display: block; margin: 20px auto; padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .close-button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="message-list">
        <h2>Upload Results</h2>
        <?php if (!empty($successMessages)): ?>
            <h3>Successes</h3>
            <?php foreach ($successMessages as $message): ?>
                <div class="success-item"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php if (!empty($errorMessages)): ?>
            <h3>Errors</h3>
            <?php foreach ($errorMessages as $message): ?>
                <div class="error-item"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>

        <button class="close-button" onclick="closePage()">Close</button>
    </div>

    <script>
        function closePage() {
            window.location.href = 'https://astradigitalsolutions.in/index.php?page=credit-entry';
        }
    </script>
</body>
</html>