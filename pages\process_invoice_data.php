<?php
// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== NULL && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        http_response_code(500);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => 'Fatal Error: ' . $error['message'],
            'debug_info' => [
                'error_details' => $error,
                'file' => $error['file'],
                'line' => $error['line']
            ]
        ]);
    }
});

// Set error handling to catch all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
});

// Start output buffering immediately
ob_start();

// Basic error reporting setup
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

try {
    // Start session and check for errors
    if (session_status() === PHP_SESSION_NONE) {
        if (!session_start()) {
            throw new Exception('Failed to start session');
        }
    }

    // Clear any existing output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set headers
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');

    // Debug logging
    error_log("Script started");
    error_log("POST data received: " . print_r($_POST, true));
    error_log("Session data: " . print_r($_SESSION, true));
    error_log("Server variables: " . print_r($_SERVER, true));

    // Try multiple possible paths for db_connect.php
    $possible_paths = [
        __DIR__ . '../db_connect.php',
        __DIR__ . '/../db_connect.php',
        __DIR__ . '/../../db_connect.php',
        dirname($_SERVER['DOCUMENT_ROOT']) . '/db_connect.php'
    ];

    $db_connect_found = false;
    foreach ($possible_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            $db_connect_found = true;
            error_log("Found db_connect.php at: " . $path);
            break;
        }
    }

    if (!$db_connect_found) {
        throw new Exception('Database connection file not found. Tried paths: ' . implode(', ', $possible_paths));
    }

    // Check database connection
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception('Database connection failed: ' . ($conn->connect_error ?? 'Connection variable not set'));
    }

    // Authentication check
    if (!isset($_SESSION['username'])) {
        throw new Exception('Unauthorized access - Please log in');
    }

    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method - Expected POST, got ' . $_SERVER['REQUEST_METHOD']);
    }

    // Check for POST data
    if (empty($_POST)) {
        throw new Exception('No POST data received. Raw input: ' . file_get_contents('php://input'));
    }

    // Rest of your existing code...
    $required_fields = ['customer', 'from_date', 'to_date', 'id', 'docket_no', 'docket_date', 'destination', 'mode_of_tsp', 'weight', 'amount'];
    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || (is_array($_POST[$field]) && empty($_POST[$field]))) {
            $missing_fields[] = $field;
        }
    }
    if (!empty($missing_fields)) {
        throw new Exception('Required fields missing: ' . implode(', ', $missing_fields));
    }

    // Validate array lengths match
    $array_fields = ['id', 'docket_no', 'docket_date', 'destination', 'mode_of_tsp', 'weight', 'amount'];
    $first_array_length = count($_POST['id']);
    foreach ($array_fields as $field) {
        if (!isset($_POST[$field]) || !is_array($_POST[$field]) || count($_POST[$field]) !== $first_array_length) {
            throw new Exception("Invalid or mismatched array length for field: $field. Expected: $first_array_length, Got: " . (isset($_POST[$field]) ? count($_POST[$field]) : 'not set'));
        }
    }

    // Get user details
    $username = $_SESSION['username'];
    $stmt = $conn->prepare("SELECT franchisee_name FROM users WHERE username = ?");
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    $stmt->bind_param("s", $username);
    if (!$stmt->execute()) {
        throw new Exception('Database execute error: ' . $stmt->error);
    }
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        throw new Exception('User not found in database');
    }
    $user = $result->fetch_assoc();
    $franchisee_name = $user['franchisee_name'];
    $stmt->close();

    // Prepare the data
    $invoice_data = [
        'customer' => $_POST['customer'],
        'from_date' => $_POST['from_date'],
        'to_date' => $_POST['to_date'],
        'franchisee_name' => $franchisee_name,
        'transactions' => []
    ];

    // Process transaction data
    for ($i = 0; $i < count($_POST['id']); $i++) {
        $invoice_data['transactions'][] = [
            'id' => $_POST['id'][$i],
            'docket_no' => $_POST['docket_no'][$i],
            'docket_date' => $_POST['docket_date'][$i],
            'destination' => $_POST['destination'][$i],
            'mode_of_tsp' => $_POST['mode_of_tsp'][$i],
            'weight' => $_POST['weight'][$i],
            'amount' => $_POST['amount'][$i]
        ];
    }

    // Store the data in session
    $_SESSION['invoice_data'] = $invoice_data;

    // Return success response
    $response = [
        'success' => true,
        'message' => 'Data processed successfully',
        'redirect' => 'generate_pdf.php'
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);

} catch (Throwable $e) {
    error_log("Error in process_invoice_data.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    
    $error_response = [
        'success' => false,
        'message' => $e->getMessage(),
        'debug_info' => [
            'error_details' => [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ],
            'request_info' => [
                'post_data_received' => !empty($_POST),
                'post_fields' => array_keys($_POST ?? []),
                'session_active' => isset($_SESSION) && !empty($_SESSION),
                'raw_input' => file_get_contents('php://input')
            ],
            'server_info' => [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
                'document_root' => $_SERVER['DOCUMENT_ROOT'],
                'script_filename' => $_SERVER['SCRIPT_FILENAME'],
                'current_dir' => __DIR__
            ]
        ]
    ];
    
    echo json_encode($error_response, JSON_PRETTY_PRINT);
} 