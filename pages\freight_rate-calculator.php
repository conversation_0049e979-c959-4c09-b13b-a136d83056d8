    <?php 
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    include 'db_connect.php';

    if (!isset($_SESSION['username'])) {
        die("<script>alert('You must be logged in!'); window.location.href='../login.php';</script>");
    }

    // Fetch user settings
    $username = $_SESSION['username'];
    $settings_stmt = $conn->prepare("SELECT fsc, gst, express_cn_cost, aircargo_cn_cost, surface_cn_cost, premium_cn_cost FROM settings WHERE username = ?");
    $settings_stmt->bind_param("s", $username);
    $settings_stmt->execute();
    $settings_result = $settings_stmt->get_result();
    $settings = $settings_result->fetch_assoc();

    // Set default values if settings don't exist
    $fsc = $settings['fsc'] ?? 0;
    $gst = $settings['gst'] ?? 0;
    $cn_costs = [
        'Express' => $settings['express_cn_cost'] ?? 0,
        'Air Cargo' => $settings['aircargo_cn_cost'] ?? 0,
        'Surface' => $settings['surface_cn_cost'] ?? 0,
        'Premium' => $settings['premium_cn_cost'] ?? 0
    ];

    // Debug information
    if (isset($_SESSION['error'])) {
        echo '<div style="background-color: #ffebee; color: #c62828; padding: 10px; margin-bottom: 10px; border-radius: 4px;">';
        echo htmlspecialchars($_SESSION['error']);
        echo '</div>';
        unset($_SESSION['error']);
    }

    if (isset($_SESSION['debug'])) {
        echo '<div style="background-color: #e3f2fd; color: #1565c0; padding: 10px; margin-bottom: 10px; border-radius: 4px;">';
        echo htmlspecialchars($_SESSION['debug']);
        echo '</div>';
        unset($_SESSION['debug']);
    }

    ?>

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Freight Rate Calculator</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        <style>
            :root {
                --primary-blue: #2196F3;
                --light-blue: #E3F2FD;
                --hover-blue: #1976D2;
                --sky-blue: #87CEEB;
                --text-dark: #2c3e50;
                --border-color: #e0e0e0;
                --background: #F8FAFC;
            }

            body {
                font-family: Arial, sans-serif;
                background: var(--background);
                margin: 0;
                padding: 0;
                min-height: 100vh;
            }

            .container {
                padding: 2rem;
                margin: 0 auto;
                max-width: 1400px;
                position: relative;
                margin-left: 0px;
                margin-top: 0px;
            }

            h2, h3 {
                color: var(--primary-blue);
                font-size: 1.75rem;
                font-weight: 600;
                margin-bottom: 1.5rem;
            }

            form {
                background: white;
                padding: 2rem;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0,0,0,0.08);
                margin-bottom: 2rem;
            }

            .form-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;
            }

            .form-group {
                margin-bottom: 0.75rem;
            }

            .form-group.full-width {
                grid-column: 1 / -1;
            }

            .button-group {
                grid-column: 1 / -1;
                margin-top: 1.5rem;
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
            }

            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                color: var(--text-dark);
                font-weight: 500;
                font-size: 0.95rem;
            }

            select, input {
                width: 100%;
                padding: 0.5rem;
                border: 2px solid var(--border-color);
                border-radius: 6px;
                font-size: 0.95rem;
                transition: all 0.3s ease;
                background: var(--background);
                height: 36px;
                box-sizing: border-box;
            }

            select:focus, input:focus {
                outline: none;
                border-color: var(--primary-blue);
                background: white;
                box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
            }

            .dimensions-section {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid var(--border-color);
            }

            .dimensions-header {
                color: var(--primary-blue);
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 1rem;
            }

            .dimensions-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .dimensions-buttons {
                display: flex;
                gap: 0.75rem;
                margin-bottom: 0.75rem;
                align-items: center;
            }

            .btn {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 6px;
                font-size: 0.95rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 100px;
                height: 36px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-primary {
                background: var(--primary-blue);
                color: white;
            }

            .btn-primary:hover {
                background: var(--hover-blue);
                transform: translateY(-1px);
            }

            .btn-secondary {
                background: #f3f4f6;
                color: var(--text-dark);
            }

            .btn-secondary:hover {
                background: #e5e7eb;
                transform: translateY(-1px);
            }

            .btn-air, .btn-sfc {
                background: var(--light-blue);
                color: var(--primary-blue);
                padding: 0.4rem 0.8rem;
                border: none;
                border-radius: 4px;
                font-size: 0.85rem;
                cursor: pointer;
                transition: all 0.2s;
                height: 32px;
            }

            .btn-air:hover, .btn-sfc:hover {
                background: var(--primary-blue);
                color: white;
            }

            table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0;
                background: white;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0,0,0,0.08);
                overflow: hidden;
                margin-bottom: 2rem;
            }

            th {
                background: var(--primary-blue);
                color: white;
                padding: 1rem;
                font-weight: 500;
                text-align: left;
                font-size: 0.95rem;
                white-space: nowrap;
            }

            td {
                padding: 1rem;
                border-bottom: 1px solid var(--border-color);
                color: var(--text-dark);
                font-size: 0.95rem;
            }

            tr:last-child td {
                border-bottom: none;
            }

            tr:hover td {
                background: var(--background);
            }

            /* Success Message Styles */
            .message {
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 4px;
                color: white;
                font-size: 16px;
                z-index: 1000;
                display: none;
                animation: slideIn 0.5s ease-out;
            }

            .message.success {
                background-color: #4CAF50;
            }

            .message.error {
                background-color: #DC2626;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            .show-message {
                display: block;
            }

            @media (max-width: 1024px) {
                .container {
                    padding: 1rem;
                }

                form {
                    padding: 1.5rem;
                }

                .form-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            @media (max-width: 768px) {
                .form-grid, .dimensions-grid {
                    grid-template-columns: 1fr;
                }

                .dimensions-buttons {
                    flex-direction: column;
                }

                .button-group {
                    flex-direction: column;
                }

                .btn {
                    width: 100%;
                }

                table {
                    display: block;
                    overflow-x: auto;
                }

                td, th {
                    min-width: 120px;
                }
            }
        </style>
    </head>
    <body>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="message error" id="errorMessage">
            <i class="fas fa-exclamation-circle"></i> 
            <?php echo htmlspecialchars($_SESSION['error']); ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['debug'])): ?>
        <div class="message success" id="debugMessage">
            <i class="fas fa-info-circle"></i> 
            <?php echo htmlspecialchars($_SESSION['debug']); ?>
        </div>
        <?php unset($_SESSION['debug']); ?>
    <?php endif; ?>

    <div class="container">
        <h2>Freight Rate Calculator</h2>

        <form action="index.php?page=process_freight_rate" method="POST">
            <div class="form-grid">
                <div class="form-group">
                    <label>Pincode:</label>
                    <input type="text" name="pincode" required placeholder="Enter pincode">
                </div>

                <div class="form-group">
                    <label>Weight in Kgs:</label>
                    <input type="number" step="0.01" name="weight" required placeholder="Enter weight">
                </div>

                <div class="form-group">
                    <label>Margin Percentage:</label>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <button type="button" class="btn-secondary" style="padding: 0.5rem; min-width: 36px;" onclick="adjustMargin(-1)">-</button>
                        <input type="number" 
                            name="margin_percentage" 
                            id="margin_percentage"
                            list="margin-options" 
                            min="0" 
                            max="1000" 
                            step="0.01" 
                            required 
                            value="100"
                            onchange="calculateRatesAjax()"
                            placeholder="Enter or select margin percentage">
                        <button type="button" class="btn-secondary" style="padding: 0.5rem; min-width: 36px;" onclick="adjustMargin(1)">+</button>
                    </div>
                    <datalist id="margin-options">
                        <?php 
                        $margins = [10, 20, 30, 40, 50, 100, 150, 200];
                        foreach ($margins as $margin) {
                            echo "<option value='$margin'>";
                        }
                        ?>
                    </datalist>
                </div>

                <div class="form-group full-width">
                    <div class="dimensions-section">
                        <div class="dimensions-header">Dimensions in centimeters (Optional)</div>
                        
                        <div class="dimensions-grid">
                            <div class="form-group">
                                <label>Length (L):</label>
                                <input type="number" step="0.01" name="length" id="length" placeholder="Enter length" oninput="updateCalculations()">
                            </div>

                            <div class="form-group">
                                <label>Breadth (B):</label>
                                <input type="number" step="0.01" name="breadth" id="breadth" placeholder="Enter breadth" oninput="updateCalculations()">
                            </div>

                            <div class="form-group">
                                <label>Height (H):</label>
                                <input type="number" step="0.01" name="height" id="height" placeholder="Enter height" oninput="updateCalculations()">
                            </div>
                        </div>

                        <div class="dimensions-buttons">
                            <div style="display: flex; align-items: center; gap: 1rem; justify-content: space-between;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <div style="display: flex; gap: 0.5rem;">
                                        <button type="button" class="btn-air" id="airBtn" onclick="calculateAir()" disabled>Air</button>
                                        <button type="button" class="btn-sfc" id="sfcBtn" onclick="calculateSFC()" disabled>SFC</button>
                                    </div>
                                    <div style="color: var(--text-dark); font-size: 0.85rem;">
                                        Press buttons to get volumetric weight
                                    </div>
                                </div>
                                <div style="display: flex; gap: 0.5rem;">
                                    <button type="submit" class="btn btn-primary">Calculate Rates</button>
                                    <button type="button" class="btn btn-secondary" onclick="clearAll()">Clear</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <div style="display: flex; gap: 2rem; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 600px;">
                <h3 style="color: var(--primary-blue); margin-top: 2rem; margin-bottom: 1rem;">Rates charged by the company</h3>
                <table style="width: 100%; border-collapse: separate; border-spacing: 0; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); overflow: hidden; table-layout: auto;">
                    <colgroup>
                        <col style="width: auto; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 15%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%;">
                    </colgroup>
                    <thead>
                        <tr>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: left; font-weight: 500;">Product</th>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: right; font-weight: 500;">Rate</th>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: right; font-weight: 500;">FSC</th>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: right; font-weight: 500;">GST</th>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: right; font-weight: 500;">CN Charges</th>
                            <th style="background: var(--primary-blue); color: white; padding: 1rem; text-align: right; font-weight: 500;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (isset($_SESSION['calculation_results']) && !empty($_SESSION['calculation_results'])): ?>
                            <?php foreach ($_SESSION['calculation_results'] as $product => $values): ?>
                            <tr>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($values['base_rate'], 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($values['base_rate'] * $fsc / 100, 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format(($values['base_rate'] + $values['base_rate'] * $fsc / 100) * $gst / 100, 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo $values['base_rate'] > 0 ? number_format($cn_costs[$product], 2) : '0.00'; ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right; font-weight: bold;"><?php 
                                    $base_rate = $values['base_rate'];
                                    $fsc_amount = $base_rate * $fsc / 100;
                                    $subtotal = $base_rate + $fsc_amount;
                                    $gst_amount = $subtotal * $gst / 100;
                                    $cn_charge = $values['base_rate'] > 0 ? $cn_costs[$product] : 0;
                                    $total = $subtotal + $gst_amount + $cn_charge;
                                    echo number_format($total, 2); 
                                ?></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <?php foreach (['Express', 'Air Cargo', 'Surface', 'Premium'] as $product): ?>
                            <tr>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div style="flex: 1; min-width: 600px;">
                <h3 style="color: #4CAF50; margin-top: 2rem; margin-bottom: 1rem;">Rates after Margin (default 100%)</h3>
                <table style="width: 100%; border-collapse: separate; border-spacing: 0; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); overflow: hidden; table-layout: auto;">
                    <colgroup>
                        <col style="width: auto; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%; border-right: 1px solid var(--border-color);">
                        <col style="width: 15%; border-right: 1px solid var(--border-color);">
                        <col style="width: 12%;">
                    </colgroup>
                    <thead>
                        <tr>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: left; font-weight: 500;">Product</th>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">Rate</th>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">FSC</th>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">GST</th>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">CN Charges</th>
                            <th style="background: #4CAF50; color: white; padding: 1rem; text-align: right; font-weight: 500;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (isset($_SESSION['calculation_results']) && !empty($_SESSION['calculation_results'])): ?>
                            <?php foreach ($_SESSION['calculation_results'] as $product => $values): ?>
                            <?php
                                $margin_rate = $values['rate']; // Use the rate with margin from calculation_results
                                $margin_fsc = $margin_rate * $fsc / 100;
                                $margin_subtotal = $margin_rate + $margin_fsc;
                                $margin_gst = $margin_subtotal * $gst / 100;
                                $margin_cn = $values['rate'] > 0 ? $cn_costs[$product] : 0;
                                $margin_total = $margin_subtotal + $margin_gst + $margin_cn;
                            ?>
                            <tr>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_rate, 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_fsc, 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo number_format($margin_gst, 2); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;"><?php echo $values['rate'] > 0 ? number_format($margin_cn, 2) : '0.00'; ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right; font-weight: bold;"><?php echo number_format($margin_total, 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <?php foreach (['Express', 'Air Cargo', 'Surface', 'Premium'] as $product): ?>
                            <tr>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);"><?php echo htmlspecialchars($product); ?></td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                                <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
    function updateCalculations() {
        const length = parseFloat(document.getElementById('length').value) || 0;
        const breadth = parseFloat(document.getElementById('breadth').value) || 0;
        const height = parseFloat(document.getElementById('height').value) || 0;
        const airBtn = document.getElementById('airBtn');
        const sfcBtn = document.getElementById('sfcBtn');
        
        const hasAllDimensions = length && breadth && height;
        airBtn.disabled = !hasAllDimensions;
        sfcBtn.disabled = !hasAllDimensions;

        if (hasAllDimensions) {
            const airWeight = Math.round((length * breadth * height / 5000) * 100) / 100;
            const sfcWeight = Math.round((length * breadth * height / 4750) * 100) / 100;
            
            airBtn.textContent = `Air: ${airWeight} kg`;
            sfcBtn.textContent = `SFC: ${sfcWeight} kg`;
        } else {
            airBtn.textContent = 'Air';
            sfcBtn.textContent = 'SFC';
        }
    }

    function calculateAir() {
        const length = parseFloat(document.getElementById('length').value);
        const breadth = parseFloat(document.getElementById('breadth').value);
        const height = parseFloat(document.getElementById('height').value);
        
        const volumetricWeight = (length * breadth * height) / 5000;
        const roundedWeight = Math.round(volumetricWeight * 100) / 100;
        
        document.querySelector('input[name="weight"]').value = roundedWeight;
    }

    function calculateSFC() {
        const length = parseFloat(document.getElementById('length').value);
        const breadth = parseFloat(document.getElementById('breadth').value);
        const height = parseFloat(document.getElementById('height').value);
        
        const volumetricWeight = (length * breadth * height) / 4750;
        const roundedWeight = Math.round(volumetricWeight * 100) / 100;
        
        document.querySelector('input[name="weight"]').value = roundedWeight;
    }

    function clearAll() {
        // Clear form inputs
        document.querySelector('form').reset();
        
        // Reset Air and SFC buttons
        document.getElementById('airBtn').textContent = 'Air';
        document.getElementById('sfcBtn').textContent = 'SFC';
        document.getElementById('airBtn').disabled = true;
        document.getElementById('sfcBtn').disabled = true;

        // Clear session storage for calculation results
        fetch('index.php?page=clear_calculation_results', {
            method: 'POST'
        }).then(() => {
            // Reset both tables to default state
            const tableRows = document.querySelectorAll('table tbody');
            tableRows.forEach(tbody => {
                tbody.innerHTML = '';
                
                ['Express', 'Air Cargo', 'Surface', 'Premium'].forEach(product => {
                    tbody.innerHTML += `
                        <tr>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color);">${product}</td>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                            <td style="padding: 1rem; border-bottom: 1px solid var(--border-color); text-align: right;">0.00</td>
                        </tr>
                    `;
                });
            });
        });
    }

    function adjustMargin(delta) {
        const marginInput = document.getElementById('margin_percentage');
        const currentValue = parseFloat(marginInput.value) || 0;
        marginInput.value = Math.max(0, Math.min(1000, currentValue + delta));
        calculateRatesAjax();
    }

    function calculateRatesAjax() {
        const pincode = document.querySelector('input[name="pincode"]').value;
        const weight = document.querySelector('input[name="weight"]').value;
        const margin_percentage = document.getElementById('margin_percentage').value;

        if (!pincode || !weight || !margin_percentage) {
            return; // Don't calculate if required fields are empty
        }

        const formData = new FormData();
        formData.append('pincode', pincode);
        formData.append('weight', weight);
        formData.append('margin_percentage', margin_percentage);

        fetch('index.php?page=process_freight_rate', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            // Refresh the tables without page reload
            fetch('index.php?page=get_calculation_tables')
            .then(response => response.text())
            .then(html => {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // Update both tables
                const tables = document.querySelectorAll('table');
                const newTables = tempDiv.querySelectorAll('table');
                tables.forEach((table, index) => {
                    if (newTables[index]) {
                        table.innerHTML = newTables[index].innerHTML;
                    }
                });
            });
        })
        .catch(error => console.error('Error:', error));
    }

    // Override form submission to use AJAX
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        calculateRatesAjax();
    });

    document.addEventListener('DOMContentLoaded', function() {
        const errorMessage = document.getElementById('errorMessage');
        const debugMessage = document.getElementById('debugMessage');
        
        if (errorMessage) {
            errorMessage.classList.add('show-message');
            setTimeout(() => {
                errorMessage.classList.remove('show-message');
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 500);
            }, 3000);
        }

        if (debugMessage) {
            debugMessage.classList.add('show-message');
            setTimeout(() => {
                debugMessage.classList.remove('show-message');
                setTimeout(() => {
                    debugMessage.style.display = 'none';
                }, 500);
            }, 3000);
        }

        // Clear tables on page load
        clearAll();
        // Set margin to 100 after clearing
        document.getElementById('margin_percentage').value = '100';
    });
    </script>

    </body>
    </html> 