<?php
session_start();
include '../db_connect.php';

if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'message' => 'Session expired']);
    exit();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if file was uploaded
if (!isset($_FILES['excelFile']) || $_FILES['excelFile']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
    exit();
}

// Require the PhpSpreadsheet library
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

try {
    $spreadsheet = IOFactory::load($_FILES['excelFile']['tmp_name']);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();

    // Remove header row
    $header = array_shift($rows);

    // Find the column indexes
    $cnnoIndex = array_search('Cnno', $header);
    $eddIndex = array_search('EDD', $header);
    $lastStatusDateIndex = array_search('Last Status Date', $header);
    $lastStatusIndex = array_search('Last Status', $header);

    if ($cnnoIndex === false || $eddIndex === false || $lastStatusDateIndex === false || $lastStatusIndex === false) {
        throw new Exception('Required columns not found in Excel file');
    }

    $username = $_SESSION['username'];
    $updatedCount = 0;
    $errors = [];
    $debugInfo = [];

    // Begin transaction
    $conn->begin_transaction();

    try {
        // First, let's verify the records exist
        $check_stmt = $conn->prepare("SELECT tr_docket_no FROM tracking WHERE tr_docket_no = ? AND username = ?");
        
        // Prepare the update statement with status
        $stmt = $conn->prepare("UPDATE tracking 
                              SET tr_edd = ?, 
                                  tr_lststs_dt = ?, 
                                  tr_delsts = ?,
                                  tr_opcl = ? 
                              WHERE tr_docket_no = ? 
                              AND username = ?");

        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        foreach ($rows as $index => $row) {
            $cnno = trim($row[$cnnoIndex]);
            if (empty($cnno)) continue;

            // Check if record exists
            $check_stmt->bind_param("ss", $cnno, $username);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if ($check_result->num_rows === 0) {
                $debugInfo[] = "Row " . ($index + 2) . ": Docket $cnno not found in tracking table";
                continue;
            }

            // Convert Excel dates to MySQL format
            $edd = !empty($row[$eddIndex]) ? 
                   date('Y-m-d', strtotime($row[$eddIndex])) : null;
            
            $lastStatusDate = !empty($row[$lastStatusDateIndex]) ? 
                             date('Y-m-d', strtotime($row[$lastStatusDateIndex])) : null;
            
            $lastStatus = trim($row[$lastStatusIndex]);
            
            // Set status to 'Closed' if delivery status is 'Delivered', otherwise set to 'Open'
            $status = (strtolower($lastStatus) === 'delivered') ? 'Closed' : 'Open';

            // Debug info
            $debugInfo[] = "Row " . ($index + 2) . ": Processing $cnno - Status: $lastStatus, tr_opcl: $status";

            $stmt->bind_param("ssssss", 
                $edd,
                $lastStatusDate,
                $lastStatus,
                $status,
                $cnno,
                $username
            );

            if ($stmt->execute()) {
                if ($stmt->affected_rows > 0) {
                    $updatedCount++;
                    $debugInfo[] = "Row " . ($index + 2) . ": Successfully updated $cnno";
                } else {
                    $debugInfo[] = "Row " . ($index + 2) . ": No changes for $cnno";
                }
            } else {
                $errors[] = "Error updating record for Cnno: $cnno - " . $stmt->error;
                $debugInfo[] = "Row " . ($index + 2) . ": Error updating $cnno - " . $stmt->error;
            }
        }

        // If no errors, commit the transaction
        if (empty($errors)) {
            $conn->commit();
            // Log debug info
            error_log("Upload Tracking Debug Info:\n" . implode("\n", $debugInfo));
            
            echo json_encode([
                'success' => true,
                'message' => "Successfully updated $updatedCount records",
                'debug' => $debugInfo // Include debug info in response
            ]);
        } else {
            throw new Exception(implode("\n", $errors));
        }

    } catch (Exception $e) {
        $conn->rollback();
        error_log("Upload Tracking Error: " . $e->getMessage() . "\nDebug Info:\n" . implode("\n", $debugInfo));
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error processing file: ' . $e->getMessage(),
        'debug' => $debugInfo ?? [] // Include debug info in error response
    ]);
}

// Close connections
if (isset($check_stmt)) $check_stmt->close();
if (isset($stmt)) $stmt->close();
if (isset($conn)) $conn->close(); 