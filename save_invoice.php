<?php
session_start();
include '../db_connect.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // First update the transactions
    foreach ($_POST['id'] as $index => $id) {
        $docket_date = $_POST['docket_date'][$index];
        $destination = $_POST['destination'][$index];
        $mode_of_tsp = $_POST['mode_of_tsp'][$index];
        $weight = $_POST['weight'][$index];
        $amount = $_POST['amount'][$index];
        $risk_charges = isset($_POST['risk_charges'][$index]) ? $_POST['risk_charges'][$index] : '0';

        // Determine whether to update owner_risk or carrier_risk field
        // For simplicity, we're going to update the owner_risk field by default
        // This could be enhanced to determine the risk type based on the value
        $sql = "UPDATE transactions SET docket_date=?, destination=?, mode_of_tsp=?, weight=?, amount=?, owner_risk=? WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssdddi", $docket_date, $destination, $mode_of_tsp, $weight, $amount, $risk_charges, $id);
        $stmt->execute();
    }

    // Calculate total amount
    $total_amount = 0;
    foreach ($_POST['amount'] as $amount) {
        $total_amount += floatval($amount);
    }

    // Calculate FSC and taxes
    $fsc_percent = isset($_POST['fsc_percent']) ? floatval($_POST['fsc_percent']) : 35;
    $fsc = $total_amount * ($fsc_percent / 100);
    $taxable_value = $total_amount + $fsc;

    // Get customer's GST applicability
    $gst_stmt = $conn->prepare("SELECT gst_apl FROM customers WHERE short_name = ?");
    $gst_stmt->bind_param("s", $_POST['customer']);
    $gst_stmt->execute();
    $gst_result = $gst_stmt->get_result();
    $gst_row = $gst_result->fetch_assoc();
    $gst_apl = $gst_row['gst_apl'] ?? 'Yes';

    // Calculate GST based on applicability
    if ($gst_apl == 'Yes') {
        $cgst = $taxable_value * 0.09;
        $sgst = $taxable_value * 0.09;
        $subtotal = $taxable_value + $cgst + $sgst;
    } else {
        $cgst = 0;
        $sgst = 0;
        $subtotal = $taxable_value;
    }

    $round_off = round($subtotal) - $subtotal;
    $grand_total = $subtotal + $round_off;

    // Get the appropriate invoice number based on GST applicability
    $invoice_number = $_POST['invoice_number'];
    if ($gst_apl == 'No') {
        // Get manual invoice settings
        $settings_stmt = $conn->prepare("SELECT manual_series, manual_invoice_number FROM settings WHERE username = ?");
        $settings_stmt->bind_param("s", $_SESSION['username']);
        $settings_stmt->execute();
        $settings_result = $settings_stmt->get_result();
        $settings_row = $settings_result->fetch_assoc();
        
        if ($settings_row) {
            $invoice_number = $settings_row['manual_series'] . $settings_row['manual_invoice_number'];
            
            // Increment manual invoice number
            $new_number = intval($settings_row['manual_invoice_number']) + 1;
            $update_stmt = $conn->prepare("UPDATE settings SET manual_invoice_number = ? WHERE username = ?");
            $update_stmt->bind_param("ss", $new_number, $_SESSION['username']);
            $update_stmt->execute();
        }
    }

    // Save to invoice table
    $save_invoice_sql = "INSERT INTO invoice (inv_cust, inv_no, inv_dt, inv_value, username) VALUES (?, ?, CURDATE(), ?, ?)";
    $save_stmt = $conn->prepare($save_invoice_sql);
    
    if (!$save_stmt) {
        die("Error preparing invoice save statement: " . $conn->error);
    }

    $save_stmt->bind_param("ssds", 
        $_POST['customer'],
        $invoice_number,
        $grand_total,
        $_SESSION['username']
    );

    if (!$save_stmt->execute()) {
        die("Error saving invoice: " . $save_stmt->error);
    }

    echo "<script>alert('Invoice Saved Successfully!'); window.location.href='../index.php?page=invoice_dashboard';</script>";
} else {
    die("Invalid Access!");
}
?>
