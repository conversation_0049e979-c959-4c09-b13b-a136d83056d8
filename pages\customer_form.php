<?php
session_start();
include 'db_connect.php'; // Database connection

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];
$editMode = false;
$customer = [
    'id' => '',
    'short_name' => '',
    'p_p' => '',
    'address1' => '',
    'address2' => '',
    'address3' => '',
    'gst_no' => '',
    'gst_apl' => 'Yes',
    'fsc_percent' => '',
    'waybill_percent' => '',
    'owner_risk' => '',
    'carrier_risk' => ''
];

// **Edit Mode: Fetch Existing Customer Data**
if (isset($_GET['id'])) {
    $editMode = true;
    $id = $_GET['id'];

    $stmt = $conn->prepare("SELECT * FROM customers WHERE id = ? AND username = ?");
    $stmt->bind_param("is", $id, $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $customer = $result->fetch_assoc();
    } else {
        die("<script>alert('Error: Customer not found.'); window.location.href='index.php?page=customer_dashboard';</script>");
    }
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $editMode ? "Edit" : "Create"; ?> Customer</title>
    <style>
        .content-box {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            font-size: 18px;
            position: relative;
            margin-left: 0px; /* Moves the box slightly to the left */
            margin-top: 0px; /* Moves the box slightly to the left */
            }

        h2 {
            text-align: center;
        }

        .form-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .form-group label {
            width: 30%;
            font-size: 16px;
            font-weight: bold;
        } 

        .form-group input, 
        .form-group select {
            width: 60%;
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 12px;
            font-size: 18px;
            background-color: #87CEEB;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }

        button:hover {
            background-color: #5CA0D3;
        }
    </style>
</head>
<body>

<div class="content-box">
    <h2><?php echo $editMode ? "Edit" : "Create"; ?> Customer</h2><br>
    <form action="index.php?page=process_customer" method="POST">
        <input type="hidden" name="id" value="<?php echo htmlspecialchars($customer['id']); ?>">
        
        <div class="form-group">
            <label for="short_name">Short Name:</label>
            <input type="text" id="short_name" name="short_name" required value="<?php echo htmlspecialchars($customer['short_name']); ?>">

            <label for="p_p">Company Name:</label>
            <input type="text" id="p_p" name="p_p" required value="<?php echo htmlspecialchars($customer['p_p']); ?>">

            <label for="address1">Address Line 1:</label>
            <input type="text" id="address1" name="address1" required value="<?php echo htmlspecialchars($customer['address1']); ?>">

            <label for="address2">Address Line 2:</label>
            <input type="text" id="address2" name="address2" value="<?php echo htmlspecialchars($customer['address2']); ?>">

            <label for="address3">Address Line 3:</label>
            <input type="text" id="address3" name="address3" value="<?php echo htmlspecialchars($customer['address3']); ?>">

            <label for="gst_no">GST No.:</label>
            <input type="text" id="gst_no" name="gst_no" required value="<?php echo htmlspecialchars($customer['gst_no']); ?>">

            <label for="gst_apl">GST Applicable:</label>
            <select id="gst_apl" name="gst_apl" required>
                <option value="Yes" <?php echo ($customer['gst_apl'] == 'Yes') ? 'selected' : ''; ?>>Yes</option>
                <option value="No" <?php echo ($customer['gst_apl'] == 'No') ? 'selected' : ''; ?>>No</option>
            </select>

            <label for="fsc_percent">FSC %:</label>
            <input type="number" id="fsc_percent" name="fsc_percent" step="0.01" required value="<?php echo htmlspecialchars($customer['fsc_percent']); ?>">

            <label for="waybill_percent">Waybill %:</label>
            <input type="number" id="waybill_percent" name="waybill_percent" step="0.01" required value="<?php echo htmlspecialchars($customer['waybill_percent']); ?>">

            <label for="owner_risk">Owner Risk %:</label>
            <input type="number" id="owner_risk" name="owner_risk" step="0.01" required value="<?php echo htmlspecialchars($customer['owner_risk']); ?>">

            <label for="carrier_risk">Carrier Risk %:</label>
            <input type="number" id="carrier_risk" name="carrier_risk" step="0.01" required value="<?php echo htmlspecialchars($customer['carrier_risk']); ?>">
        </div>

        <button type="submit"><?php echo $editMode ? "Update" : "Create"; ?> Customer</button>
    </form>
</div>

</body>
</html>
