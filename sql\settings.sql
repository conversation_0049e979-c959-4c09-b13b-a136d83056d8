-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jun 29, 2025 at 06:12 AM
-- Server version: 10.11.10-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u111133901_test_alm`
--

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `express_cn_cost` decimal(10,2) NOT NULL,
  `aircargo_cn_cost` decimal(10,2) NOT NULL,
  `surface_cn_cost` decimal(10,2) NOT NULL,
  `premium_cn_cost` decimal(10,2) NOT NULL,
  `region_series` varchar(50) NOT NULL,
  `fsc` decimal(5,2) NOT NULL COMMENT 'Fuel Surcharge Percentage',
  `gst` decimal(5,2) NOT NULL COMMENT 'GST Percentage',
  `metro_status` enum('Metro','Non-Metro') NOT NULL,
  `billing_zone` varchar(50) NOT NULL,
  `invoice_temp` enum('Regular','Template-1','Template-2') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `invoice_series` varchar(255) DEFAULT NULL,
  `invoice_number` varchar(50) DEFAULT NULL,
  `manual_series` varchar(255) DEFAULT NULL,
  `manual_invoice_number` varchar(50) DEFAULT '0001',
  `surface_docket` varchar(255) DEFAULT '',
  `aircargo_docket` varchar(255) DEFAULT '',
  `premium_docket` varchar(255) DEFAULT '',
  `ptp_docket` varchar(255) DEFAULT '',
  `cod_docket` varchar(255) DEFAULT '',
  `international_docket` varchar(255) DEFAULT '',
  `ecom_express_docket` varchar(255) DEFAULT '',
  `ecom_surface_docket` varchar(255) DEFAULT ''
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `username`, `express_cn_cost`, `aircargo_cn_cost`, `surface_cn_cost`, `premium_cn_cost`, `region_series`, `fsc`, `gst`, `metro_status`, `billing_zone`, `invoice_temp`, `created_at`, `invoice_series`, `invoice_number`, `manual_series`, `manual_invoice_number`, `surface_docket`, `aircargo_docket`, `premium_docket`, `ptp_docket`, `cod_docket`, `international_docket`, `ecom_express_docket`, `ecom_surface_docket`) VALUES
(1, 'BM999', 8.00, 17.00, 17.00, 25.00, 'M', 35.00, 18.00, 'Metro', 'Mumbai', 'Regular', '2025-03-12 05:43:12', 'ABC/2023-2024/', '0068', NULL, '0001', 'D', 'D', 'V', 'E', 'I', 'N', '7X', '7D'),
(2, 'demo', 10.00, 20.00, 20.00, 25.00, 'M', 35.00, 18.00, 'Metro', 'Mumbai', 'Template-2', '2025-03-21 15:50:04', 'DEMO/2024-25/', '0032', NULL, '0001', 'D', 'D', 'V', 'PE', 'I', 'N', '7X', '7D'),
(3, 'BM1001', 8.89, 10.02, 10.02, 45.26, 'M', 35.00, 18.00, 'Metro', 'Mumbai', 'Template-1', '2025-03-22 14:26:13', 'SDE-2025-26-', '0095', '', '001', 'D', 'D', 'V', 'P', 'I', 'N', '7X', '7D'),
(4, 'MF2053', 8.00, 15.00, 15.00, 17.00, 'M', 35.00, 18.00, 'Metro', 'Mumbai', 'Template-1', '2025-04-01 07:22:35', 'SVE-2025-26-', '1380', 'SVE-M-', '0009', 'D', 'D', 'V', 'E', 'I', 'N', '7X', '7D'),
(5, 'MF2648', 8.39, 10.77, 10.77, 45.26, 'M', 35.00, 18.00, 'Metro', 'Mumbai', 'Template-1', '2025-05-29 06:44:50', 'DNE/25-26/', '0014', 'DNE-', '0002', 'D', 'D', 'V', 'E', 'I', 'N', '7X', '7D');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_settings_username` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
