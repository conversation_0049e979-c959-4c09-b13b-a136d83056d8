<?php
session_start();

// Database Connection for InfinityFree

$servername = "localhost";
$username = "u738397982_astra";
$password = "<PERSON><PERSON><PERSON>@0505";
$database = "u738397982_astra_logistic";



$conn = new mysqli($servername, $username, $password, $database);



// Check connection

if ($conn->connect_error) {

    die("Connection failed: " . $conn->connect_error);

}



// Function to generate the next username (BM999, BM1000, etc.)

function generateUsername($conn) {

    $query = "SELECT username FROM users WHERE username LIKE 'BM%' ORDER BY id DESC LIMIT 1";

    $result = $conn->query($query);

    

    if ($result->num_rows > 0) {

        $row = $result->fetch_assoc();

        $lastUsername = $row['username'];

        $number = intval(substr($lastUsername, 2)) + 1; // Extract number and increment

    } else {

        $number = 999; // Start from BM999

    }



    return "BM" . $number;

}



// Handle Razorpay payment response

if (isset($_GET['payment_id']) || isset($_POST['razorpay_payment_id'])) {

    // Get payment ID from either GET or POST

    $payment_id = $_GET['payment_id'] ?? $_POST['razorpay_payment_id'];

    

    if (isset($_SESSION['pending_registration'])) {

        $registrationData = $_SESSION['pending_registration'];

        $email = $registrationData['email'];

        $password = $registrationData['password'];

        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        $username = generateUsername($conn);



        // Insert new user into the database

        $stmt = $conn->prepare("INSERT INTO users (username, email, password_hash, payment_id) VALUES (?, ?, ?, ?)");

        $stmt->bind_param("ssss", $username, $email, $hashedPassword, $payment_id);

        

        if ($stmt->execute()) {

            unset($_SESSION['pending_registration']);

            header("Location: register.php?success=1&username=$username");

            exit();

        } else {

            echo "<script>alert('Error: Registration failed. Try again!'); window.history.back();</script>";

        }



        $stmt->close();

    } else {

        echo "<script>alert('No pending registration found. Please start over.'); window.location.href = 'register.php';</script>";

    }

}

// Handle initial form submission

else if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $email = trim($_POST['email']);

    $password = $_POST['password'];



    // Check if email already exists

    $checkEmail = $conn->prepare("SELECT id FROM users WHERE email = ?");

    $checkEmail->bind_param("s", $email);

    $checkEmail->execute();

    $checkEmail->store_result();



    if ($checkEmail->num_rows > 0) {

        echo "<script>alert('Error: Email already registered!'); window.history.back();</script>";

        exit();

    }

    $checkEmail->close();



    // Store registration data in session

    $_SESSION['pending_registration'] = [

        'email' => $email,

        'password' => $password

    ];

}



$conn->close();

?>



<!DOCTYPE html>

<html lang="en">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>User Registration</title>
    <link rel="icon" type="image/x-icon" href="images/favicon.ico" />  
    <link rel="stylesheet" href="styles.css">

    <style>

        .content-box {

            max-width: 400px;

            margin: 50px auto;

            padding: 30px;

            background: white;

            border-radius: 10px;

            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

            font-size: 20px;

        }



        h2 {

            font-size: 28px;

            text-align: center;

            margin-bottom: 20px;

        }



        label {

            display: block;

            margin-top: 15px;

            font-size: 20px;

            font-weight: bold;

        }



        input {

            width: 100%;

            padding: 12px;

            font-size: 18px;

            margin-top: 5px;

            border: 1px solid #ccc;

            border-radius: 5px;

        }



        button {

            width: 100%;

            padding: 15px;

            font-size: 20px;

            background-color: #87CEEB;

            color: white;

            border: none;

            border-radius: 5px;

            cursor: pointer;

            margin-top: 20px;

        }



        button:hover {

            background-color: #5CA0D3;

        }



        .success-box {

            max-width: 400px;

            margin: 50px auto;

            padding: 20px;

            background: #d4edda;

            color: #155724;

            border-radius: 10px;

            text-align: center;

            font-size: 20px;

            border: 1px solid #c3e6cb;

        }

    </style>

</head>

<body>



    <?php if (isset($_GET['success']) && isset($_GET['username'])): ?>

        <div class="success-box">

            🎉 Registration Successful!<br>

            Your assigned username: <strong><?php echo htmlspecialchars($_GET['username']); ?></strong>

            <br><br>

            <a href="index.php"><button>Go to Login</button></a>

        </div>

    <?php elseif (isset($_SESSION['pending_registration'])): ?>

        <div class="content-box">

            <h2>Complete Payment</h2>

            <p>Please complete the payment to finish registration.</p>

            <form>

                <script src="https://checkout.razorpay.com/v1/payment-button.js" 

                        data-payment_button_id="pl_PYxp58M8PW9bW3" 

                        async>

                </script>

            </form>

        </div>

    <?php else: ?>

        <div class="content-box">

            <h2>User Registration</h2>

            <form action="register.php" method="POST">

                <label for="email">Email:</label>

                <input type="email" id="email" name="email" required>



                <label for="password">Password:</label>

                <input type="password" id="password" name="password" required>



                <button type="submit">Continue to Payment</button>

            </form>

        </div>

    <?php endif; ?>



</body>

</html>

