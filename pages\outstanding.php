<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    $_SESSION['alert_message'] = "Error: You must be logged in.";
    $_SESSION['alert_type'] = 'error';
    header("Location: login.php");
    exit();
}

$username = $_SESSION['username'];
$search = isset($_GET['search']) ? $_GET['search'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Handle form submission for updating payment status
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_payment'])) {
        $id = $_POST['id'];
        $payment_status = $_POST['payment_status'];
        $paid_amount = $_POST['paid_amount'];
        $payment_date = $_POST['payment_date'];

        // Validate payment date when status is Paid
        if ($payment_status === 'Paid' && empty($payment_date)) {
            $_SESSION['alert_message'] = "Payment date is required when status is Paid";
            $_SESSION['alert_type'] = 'error';
            
            $redirect_url = "index.php?page=outstanding";
            if (!empty($search)) $redirect_url .= "&search=" . urlencode($search);
            if (!empty($start_date)) $redirect_url .= "&start_date=" . urlencode($start_date);
            if (!empty($end_date)) $redirect_url .= "&end_date=" . urlencode($end_date);
            
            echo "<script>window.location.href = '" . $redirect_url . "';</script>";
            exit();
        }

        $stmt = $conn->prepare("UPDATE invoice SET inv_paysts = ?, inv_paid_amt = ?, inv_paid_dt = ? WHERE id = ? AND username = ?");
        $stmt->bind_param("sssis", $payment_status, $paid_amount, $payment_date, $id, $username);
        
        if ($stmt->execute()) {
            $_SESSION['alert_message'] = "Payment status updated successfully!";
            $_SESSION['alert_type'] = 'success';
        } else {
            $_SESSION['alert_message'] = "Error updating payment status: " . $stmt->error;
            $_SESSION['alert_type'] = 'error';
        }
    } elseif (isset($_POST['delete_invoice'])) {
        $id = $_POST['id'];
        
        $stmt = $conn->prepare("DELETE FROM invoice WHERE id = ? AND username = ?");
        $stmt->bind_param("is", $id, $username);
        
        if ($stmt->execute()) {
            $_SESSION['alert_message'] = "Invoice deleted successfully!";
            $_SESSION['alert_type'] = 'success';
        } else {
            $_SESSION['alert_message'] = "Error deleting invoice: " . $stmt->error;
            $_SESSION['alert_type'] = 'error';
        }
    }
    
    $redirect_url = "index.php?page=outstanding";
    if (!empty($search)) $redirect_url .= "&search=" . urlencode($search);
    if (!empty($start_date)) $redirect_url .= "&start_date=" . urlencode($start_date);
    if (!empty($end_date)) $redirect_url .= "&end_date=" . urlencode($end_date);
    
    echo "<script>window.location.href = '" . $redirect_url . "';</script>";
    exit();
}

// Build the query with filters
$query = "SELECT * FROM invoice WHERE username = ?";
$params = [$username];
$types = "s";

if (!empty($search)) {
    $query .= " AND (inv_cust LIKE ? OR inv_no LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $types .= "ss";
}

if (!empty($start_date)) {
    $query .= " AND inv_dt >= ?";
    $params[] = $start_date;
    $types .= "s";
}

if (!empty($end_date)) {
    $query .= " AND inv_dt <= ?";
    $params[] = $end_date;
    $types .= "s";
}

$query .= " ORDER BY inv_dt DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Loading and initial visibility control */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden; /* Prevent scrolling during load */
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Existing critical CSS */
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        /* Essential layout and container styles */
        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        /* Critical header section styles */
        .header-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        /* Critical search section styles */
        .search-section {
            flex: 1;
            max-width: 800px;
            min-width: 280px;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        /* Critical search input styles */
        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
        }

        .date-input {
            min-width: 150px;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            background: var(--background);
        }

        .search-box:focus, 
        .date-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
        }

        .btn-search {
            padding: 0.75rem 1.5rem;
            border: none;
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            font-size: 0.85rem;
            border-radius: 6px;
            font-weight: 500;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .clear-search {
            padding: 0.75rem 1.5rem;
            border: none;
            background: var(--light-blue);
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 0.85rem;
            border-radius: 6px;
            font-weight: 500;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Critical table styles */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        .table tbody td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        /* Critical form element styles */
        .editable {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            padding: 0.5rem;
            border-radius: 4px;
            width: 100%;
        }

        .btn-save {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        /* Critical responsive styles */
        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-section {
                order: 2;
            }
            
            .search-group {
                flex-direction: column;
                width: 100%;
            }
            
            .search-box, 
            .date-input, 
            .btn-search, 
            .clear-search {
                width: 100%;
            }
            
            .table thead {
                display: none;
            }
            
            .table tbody td {
                display: block;
                padding: 0.5rem 1rem;
                text-align: right;
                border: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <!-- Report Component -->
        <div class="report-container">
            <div class="header-section">
                <h1 class="page-title">Outstanding Invoices</h1>
                <div class="search-section">
                    <div class="search-container">
                        <form method="GET" action="index.php" id="searchForm">
                            <input type="hidden" name="page" value="outstanding">
                            <div class="search-group">
                                <input type="text" 
                                       id="searchBox" 
                                       name="search"
                                       class="search-box" 
                                       placeholder="Search by customer or invoice number..."
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       autocomplete="off">
                                <input type="date" 
                                       id="startDate" 
                                       name="start_date" 
                                       class="date-input" 
                                       value="<?php echo htmlspecialchars($start_date); ?>"
                                       placeholder="Start Date">
                                <input type="date" 
                                       id="endDate" 
                                       name="end_date" 
                                       class="date-input" 
                                       value="<?php echo htmlspecialchars($end_date); ?>"
                                       placeholder="End Date">
                                <button type="submit" class="btn-search">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <button type="button" id="clearSearch" class="clear-search">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="total-badge">
                    <i class="fas fa-file-alt"></i>
                    <span>Total Records: <span id="recordCount"><?php echo $result->num_rows; ?></span></span>
                </div>
            </div>

            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Invoice No</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Payment Status</th>
                            <th>Amount Paid</th>
                            <th>Payment Date</th>
                            <th>Balance</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td data-label="customer"><?php echo htmlspecialchars($row['inv_cust']); ?></td>
                            <td data-label="invoice no"><?php echo htmlspecialchars($row['inv_no']); ?></td>
                            <td data-label="date"><?php echo date('d-M-Y', strtotime($row['inv_dt'])); ?></td>
                            <td data-label="amount">₹<?php echo number_format($row['inv_value'], 2); ?></td>
                            <td data-label="payment status">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                    <input type="hidden" name="update_payment" value="1">
                                    <select name="payment_status" class="editable" required onchange="togglePaymentFields(this)">
                                        <option value="Pending" <?php echo ($row['inv_paysts'] == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                        <option value="Partial" <?php echo ($row['inv_paysts'] == 'Partial') ? 'selected' : ''; ?>>Partial</option>
                                        <option value="Paid" <?php echo ($row['inv_paysts'] == 'Paid') ? 'selected' : ''; ?>>Paid</option>
                                    </select>
                            </td>
                            <td data-label="amount paid">
                                    <input type="number" name="paid_amount" 
                                           class="editable payment-field" 
                                           value="<?php echo htmlspecialchars($row['inv_paid_amt']); ?>"
                                           step="0.01" required
                                           <?php echo ($row['inv_paysts'] == 'Paid') ? 'disabled' : ''; ?>>
                            </td>
                            <td data-label="payment date">
                                    <input type="date" name="payment_date" 
                                           class="editable payment-field" 
                                           value="<?php echo htmlspecialchars($row['inv_paid_dt']); ?>"
                                           <?php echo ($row['inv_paysts'] == 'Paid') ? 'required disabled' : ''; ?>>
                            </td>
                            <td data-label="balance amount">
                                    <?php 
                                    $balance = $row['inv_value'] - ($row['inv_paid_amt'] ?? 0);
                                    echo '₹' . number_format($balance, 2);
                                    ?>
                            </td>
                            <td data-label="actions">
                                    <button type="submit" class="btn-save" onclick="return validateForm(this)">
                                        <i class="fas fa-save"></i> Save
                                    </button>
                                </form>
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this invoice?');">
                                    <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                    <input type="hidden" name="delete_invoice" value="1">
                                    <button type="submit" class="btn-delete">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Non-critical CSS -->
    <style>
        /* Additional styles for enhanced features */
        .btn-search:hover,
        .btn-save:hover {
            background: var(--hover-blue);
            transition: all 0.2s ease;
        }

        .clear-search:hover {
            background: var(--primary-blue);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-delete:hover {
            background: #c82333;
            transition: all 0.2s ease;
        }

        .search-box,
        .date-input {
            transition: all 0.3s ease;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        /* Additional responsive styles */
        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .search-container {
                flex-direction: column;
            }

            .total-badge {
                align-self: flex-start;
            }

            .table tbody tr {
                display: block;
                border-bottom: 1px solid var(--border-color);
                padding: 0.5rem 0;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: var(--primary-blue);
            }
        }
    </style>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');
        
        // Ensure the loading overlay is visible initially
        loadingOverlay.style.visibility = 'visible';
        loadingOverlay.style.opacity = '1';
        
        // Hide main content initially
        mainContent.style.visibility = 'hidden';
        mainContent.style.opacity = '0';
        
        // Function to show content
        function showContent() {
            // First hide the loading overlay
            loadingOverlay.style.opacity = '0';
            
            // After a brief delay, show the main content
            setTimeout(() => {
                // Hide loading overlay completely
                loadingOverlay.style.visibility = 'hidden';
                
                // Show main content
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto'; // Re-enable scrolling
                
                // Show report container with a slight delay for smooth transition
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }
        
        // Wait for everything to load
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }

        const searchBox = $('#searchBox');
        const startDate = $('#startDate');
        const endDate = $('#endDate');
        const clearButton = $('#clearSearch');
        const searchForm = $('#searchForm');

        function updateClearButton() {
            const hasValue = searchBox.val().trim() !== '' || startDate.val() !== '' || endDate.val() !== '';
            clearButton.toggle(hasValue);
        }

        searchBox.on('input', updateClearButton);
        startDate.on('change', updateClearButton);
        endDate.on('change', updateClearButton);

        clearButton.on('click', function() {
            searchBox.val('');
            startDate.val('');
            endDate.val('');
            clearButton.hide();
            searchForm.submit();
        });

        updateClearButton();

        // Initialize payment fields on page load
        document.querySelectorAll('select[name="payment_status"]').forEach(select => {
            togglePaymentFields(select);
        });
    });

    // Function to toggle payment fields based on status
    function togglePaymentFields(selectElement) {
        const form = selectElement.closest('form');
        const paymentFields = form.querySelectorAll('.payment-field');
        const status = selectElement.value;
        
        if (status === 'Paid') {
            paymentFields.forEach(field => {
                field.required = true;
                field.disabled = true;
                field.style.borderColor = '#e0e0e0';
                field.style.backgroundColor = '#f8f9fa';
                field.style.cursor = 'not-allowed';
            });
        } else if (status === 'Partial') {
            paymentFields.forEach(field => {
                if (field.name === 'paid_amount') {
                    field.required = true;
                    field.disabled = false;
                    field.style.borderColor = '#ffeeba';
                    field.style.backgroundColor = '#fff3cd';
                    field.style.cursor = 'text';
                } else {
                    field.required = false;
                    field.disabled = false;
                    field.style.borderColor = '#e0e0e0';
                    field.style.backgroundColor = '#F8FAFC';
                    field.style.cursor = 'text';
                }
            });
        } else {
            paymentFields.forEach(field => {
                field.required = false;
                field.disabled = false;
                field.style.borderColor = '#e0e0e0';
                field.style.backgroundColor = '#F8FAFC';
                field.style.cursor = 'text';
            });
        }
    }

    // Function to validate form before submission
    function validateForm(button) {
        const form = button.closest('form');
        const status = form.querySelector('select[name="payment_status"]').value;
        const paidAmount = form.querySelector('input[name="paid_amount"]').value;
        const paymentDate = form.querySelector('input[name="payment_date"]').value;

        if (status === 'Paid' && (!paidAmount || !paymentDate)) {
            alert('Please fill in both amount paid and payment date when status is Paid');
            return false;
        }

        if (status === 'Partial' && !paidAmount) {
            alert('Please fill in amount paid when status is Partial');
            return false;
        }

        return true;
    }
    </script>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?> 