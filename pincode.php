<?php
// Include database connection
require_once 'db_connect.php';

// Fetch unique values for dropdowns
$unique_zones = [];
$unique_ts_zones = [];
$unique_pr_zones = [];
$unique_states = [];

// Get unique zones
$zone_query = "SELECT DISTINCT zone FROM pincode_data WHERE zone IS NOT NULL AND zone != '' ORDER BY zone";
$result = $conn->query($zone_query);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $unique_zones[] = $row['zone'];
    }
}

// Get unique TS zones
$ts_zone_query = "SELECT DISTINCT ts_zone FROM pincode_data WHERE ts_zone IS NOT NULL AND ts_zone != '' ORDER BY ts_zone";
$result = $conn->query($ts_zone_query);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $unique_ts_zones[] = $row['ts_zone'];
    }
}

// Get unique PR zones
$pr_zone_query = "SELECT DISTINCT pr_zone FROM pincode_data WHERE pr_zone IS NOT NULL AND pr_zone != '' ORDER BY pr_zone";
$result = $conn->query($pr_zone_query);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $unique_pr_zones[] = $row['pr_zone'];
    }
}

// Get unique states
$state_query = "SELECT DISTINCT state FROM pincode_data WHERE state IS NOT NULL AND state != '' ORDER BY state";
$result = $conn->query($state_query);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $unique_states[] = $row['state'];
    }
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $stmt = $conn->prepare("INSERT INTO pincode_data (pincode, city_name, zone, ts_zone, pr_zone, state) 
                                      VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("ssssss", 
                    $_POST['pincode'],
                    $_POST['city_name'],
                    $_POST['zone'],
                    $_POST['ts_zone'],
                    $_POST['pr_zone'],
                    $_POST['state']
                );
                $stmt->execute();
                break;

            case 'edit':
                $stmt = $conn->prepare("UPDATE pincode_data SET 
                                      pincode = ?,
                                      city_name = ?,
                                      zone = ?,
                                      ts_zone = ?,
                                      pr_zone = ?,
                                      state = ?
                                      WHERE id = ?");
                $stmt->bind_param("ssssssi", 
                    $_POST['pincode'],
                    $_POST['city_name'],
                    $_POST['zone'],
                    $_POST['ts_zone'],
                    $_POST['pr_zone'],
                    $_POST['state'],
                    $_POST['id']
                );
                $stmt->execute();
                break;
        }
    }
}

// Handle search
$search_results = [];
if (isset($_GET['search'])) {
    $search = $_GET['search'];
    $stmt = $conn->prepare("SELECT * FROM pincode_data WHERE 
                           pincode LIKE ? OR 
                           city_name LIKE ? OR 
                           state LIKE ?");
    $search_param = "%$search%";
    $stmt->bind_param("sss", $search_param, $search_param, $search_param);
    $stmt->execute();
    $result = $stmt->get_result();
    $search_results = $result->fetch_all(MYSQLI_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pincode Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .form-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .search-results {
            margin-top: 20px;
        }
        .select2-container {
            width: 100% !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="text-center my-4">Pincode Management System</h2>
        
        <!-- Search Form -->
        <div class="form-container">
            <form method="GET" action="index.php" class="mb-4">
                <input type="hidden" name="page" value="pincode">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Search by pincode, city or state" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>

            <!-- Add/Edit Form -->
            <form method="POST" action="index.php?page=pincode" id="pincodeForm">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="id" id="editId">
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="pincode" class="form-label">Pincode</label>
                        <input type="text" class="form-control" id="pincode" name="pincode" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="city_name" class="form-label">City Name</label>
                        <input type="text" class="form-control" id="city_name" name="city_name" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="zone" class="form-label">Zone</label>
                        <select class="form-select select2" id="zone" name="zone">
                            <option value="">Select Zone</option>
                            <?php foreach ($unique_zones as $zone): ?>
                                <option value="<?php echo htmlspecialchars($zone); ?>"><?php echo htmlspecialchars($zone); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="ts_zone" class="form-label">TS Zone</label>
                        <select class="form-select select2" id="ts_zone" name="ts_zone">
                            <option value="">Select TS Zone</option>
                            <?php foreach ($unique_ts_zones as $ts_zone): ?>
                                <option value="<?php echo htmlspecialchars($ts_zone); ?>"><?php echo htmlspecialchars($ts_zone); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="pr_zone" class="form-label">PR Zone</label>
                        <select class="form-select select2" id="pr_zone" name="pr_zone">
                            <option value="">Select PR Zone</option>
                            <?php foreach ($unique_pr_zones as $pr_zone): ?>
                                <option value="<?php echo htmlspecialchars($pr_zone); ?>"><?php echo htmlspecialchars($pr_zone); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="state" class="form-label">State</label>
                    <select class="form-select select2" id="state" name="state">
                        <option value="">Select State</option>
                        <?php foreach ($unique_states as $state): ?>
                            <option value="<?php echo htmlspecialchars($state); ?>"><?php echo htmlspecialchars($state); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary" id="submitBtn">Add New Pincode</button>
                <button type="button" class="btn btn-secondary" id="clearBtn">Clear Form</button>
            </form>
        </div>

        <!-- Search Results -->
        <?php if (!empty($search_results)): ?>
        <div class="search-results">
            <h3>Search Results</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Pincode</th>
                        <th>City</th>
                        <th>Zone</th>
                        <th>TS Zone</th>
                        <th>PR Zone</th>
                        <th>State</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($search_results as $result): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($result['pincode']); ?></td>
                        <td><?php echo htmlspecialchars($result['city_name']); ?></td>
                        <td><?php echo htmlspecialchars($result['zone']); ?></td>
                        <td><?php echo htmlspecialchars($result['ts_zone']); ?></td>
                        <td><?php echo htmlspecialchars($result['pr_zone']); ?></td>
                        <td><?php echo htmlspecialchars($result['state']); ?></td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" 
                                    data-id="<?php echo $result['id']; ?>"
                                    data-pincode="<?php echo htmlspecialchars($result['pincode']); ?>"
                                    data-city="<?php echo htmlspecialchars($result['city_name']); ?>"
                                    data-zone="<?php echo htmlspecialchars($result['zone']); ?>"
                                    data-ts-zone="<?php echo htmlspecialchars($result['ts_zone']); ?>"
                                    data-pr-zone="<?php echo htmlspecialchars($result['pr_zone']); ?>"
                                    data-state="<?php echo htmlspecialchars($result['state']); ?>">
                                Edit
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                placeholder: 'Select an option',
                allowClear: true
            });

            const form = document.getElementById('pincodeForm');
            const submitBtn = document.getElementById('submitBtn');
            const clearBtn = document.getElementById('clearBtn');
            const editBtns = document.querySelectorAll('.edit-btn');

            // Handle edit button clicks
            editBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const data = this.dataset;
                    document.getElementById('editId').value = data.id;
                    document.getElementById('pincode').value = data.pincode;
                    document.getElementById('city_name').value = data.city;
                    
                    // Set dropdown values
                    $('#zone').val(data.zone).trigger('change');
                    $('#ts_zone').val(data.tsZone).trigger('change');
                    $('#pr_zone').val(data.prZone).trigger('change');
                    $('#state').val(data.state).trigger('change');
                    
                    document.getElementById('formAction').value = 'edit';
                    submitBtn.textContent = 'Update Pincode';
                });
            });

            // Handle clear button
            clearBtn.addEventListener('click', function() {
                form.reset();
                $('.select2').val('').trigger('change');
                document.getElementById('formAction').value = 'add';
                document.getElementById('editId').value = '';
                submitBtn.textContent = 'Add New Pincode';
            });
        });
    </script>
</body>
</html> 