<?php
session_start();
include 'db_connect.php'; // Database connection

if (!isset($_SESSION['username'])) {
    die("<script>alert('Error: You must be logged in.'); window.location.href='../login.php';</script>");
}

$username = $_SESSION['username'];

// Fetch customers for the logged-in user
$query = "SELECT * FROM customers WHERE username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: var(--background);
        }

        .content-box {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-left: 0px;
            margin-top: 0px;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0 0 2rem 0;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: var(--background);
        }

        .action-buttons a {
            text-decoration: none;
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .edit-btn {
            background-color: #ffc107;
            color: black;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
        }

        .create-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.95rem;
            font-weight: 500;
            margin: 0 auto 2rem auto;
            transition: all 0.3s ease;
        }

        .create-btn:hover {
            transform: translateY(-1px);
        }

        /* Search Box Styles */
        .search-container {
            margin-bottom: 2rem;
        }

        .search-box {
            position: relative;
            max-width: 500px;
            margin: 0 auto 1rem auto;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 3rem 0.75rem 2.5rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 1rem;
        }

        .clear-btn {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background-color: var(--light-blue);
            color: var(--primary-blue);
        }

        .search-info {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
        }

        .loading-indicator {
            color: var(--primary-blue);
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .content-box {
                padding: 1rem;
                margin: 1rem;
            }

            .search-box {
                max-width: 100%;
            }

            .search-info {
                flex-direction: column;
                gap: 0.5rem;
            }

            table {
                display: block;
                overflow-x: auto;
            }

            th, td {
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>

<div class="content-box">
    <h2><i class="fas fa-users"></i> Customer Dashboard</h2>

    <a href="index.php?page=customer_form" class="create-btn">
        <i class="fas fa-plus"></i> Create New Customer
    </a>

    <!-- Search Box -->
    <div class="search-container">
        <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="customerSearch" placeholder="Search customers by name, company, or GST number..." autocomplete="off">
            <button type="button" id="clearSearch" class="clear-btn" style="display: none;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="search-info">
            <span id="recordCount"><?php echo $result->num_rows; ?></span> customers found
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i> Searching...
            </div>
        </div>
    </div>

    <table>
        <thead>
        <tr>
            <th>Short Name</th>
            <th>Company Name</th>
            <th>GST No.</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody id="customerTableBody">
        <?php while ($row = $result->fetch_assoc()): ?>
            <tr>
                <td><?php echo htmlspecialchars($row['short_name']); ?></td>
                <td><?php echo htmlspecialchars($row['p_p']); ?></td>
                <td><?php echo htmlspecialchars($row['gst_no']); ?></td>
                <td class="action-buttons">
                    <a href="index.php?page=customer_form&id=<?php echo $row['id']; ?>" class="edit-btn">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="index.php?page=delete_customer&id=<?php echo $row['id']; ?>" class="delete-btn" onclick="return confirm('Are you sure?');">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                </td>
            </tr>
        <?php endwhile; ?>
        </tbody>
    </table>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    const searchBox = $('#customerSearch');
    const clearButton = $('#clearSearch');
    const loadingIndicator = $('#loadingIndicator');
    const tbody = $('#customerTableBody');
    let searchTimer;

    function showError(message) {
        tbody.empty();
        tbody.append(`
            <tr>
                <td colspan="4" style="text-align: center; padding: 2rem;">
                    <div style="color: #DC2626;">${message}</div>
                </td>
            </tr>
        `);
    }

    function showNoResults() {
        tbody.empty();
        tbody.append(`
            <tr>
                <td colspan="4" style="text-align: center; padding: 2rem;">
                    <div style="color: #666;">No customers found matching your search</div>
                </td>
            </tr>
        `);
    }

    // Search functionality
    searchBox.on('input', function() {
        const searchValue = $(this).val().trim();
        clearButton.toggle(searchValue.length > 0);

        clearTimeout(searchTimer);

        if (searchValue.length > 0) {
            loadingIndicator.show();

            searchTimer = setTimeout(() => {
                $.ajax({
                    url: 'index.php?page=customer_search',
                    data: { search: searchValue },
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        loadingIndicator.hide();

                        if (response.success) {
                            tbody.empty();
                            $('#recordCount').text(response.total);

                            if (response.data.length === 0) {
                                showNoResults();
                                return;
                            }

                            response.data.forEach(function(customer) {
                                const row = `
                                    <tr>
                                        <td>${escapeHtml(customer.short_name)}</td>
                                        <td>${escapeHtml(customer.p_p)}</td>
                                        <td>${escapeHtml(customer.gst_no)}</td>
                                        <td class="action-buttons">
                                            <a href="index.php?page=customer_form&id=${customer.id}" class="edit-btn">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="index.php?page=delete_customer&id=${customer.id}" class="delete-btn" onclick="return confirm('Are you sure?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </td>
                                    </tr>
                                `;
                                tbody.append(row);
                            });
                        } else {
                            showError(response.message || 'Search failed');
                        }
                    },
                    error: function(xhr, status, error) {
                        loadingIndicator.hide();
                        console.error('AJAX error:', {xhr, status, error});
                        showError('Search failed. Please try again.');
                    }
                });
            }, 300); // 300ms delay for debouncing
        } else {
            // If search is empty, reload all customers
            loadingIndicator.show();

            searchTimer = setTimeout(() => {
                $.ajax({
                    url: 'index.php?page=customer_search',
                    data: { search: '' },
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        loadingIndicator.hide();

                        if (response.success) {
                            tbody.empty();
                            $('#recordCount').text(response.total);

                            response.data.forEach(function(customer) {
                                const row = `
                                    <tr>
                                        <td>${escapeHtml(customer.short_name)}</td>
                                        <td>${escapeHtml(customer.p_p)}</td>
                                        <td>${escapeHtml(customer.gst_no)}</td>
                                        <td class="action-buttons">
                                            <a href="index.php?page=customer_form&id=${customer.id}" class="edit-btn">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="index.php?page=delete_customer&id=${customer.id}" class="delete-btn" onclick="return confirm('Are you sure?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </td>
                                    </tr>
                                `;
                                tbody.append(row);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        loadingIndicator.hide();
                        console.error('AJAX error:', {xhr, status, error});
                    }
                });
            }, 100);
        }
    });

    // Clear search functionality
    clearButton.on('click', function() {
        searchBox.val('').trigger('input');
        searchBox.focus();
    });

    // Utility function to escape HTML
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text ? text.replace(/[&<>"']/g, function(m) { return map[m]; }) : '';
    }
});
</script>

</body>
</html>

<?php
$stmt->close();
$conn->close();
?>
