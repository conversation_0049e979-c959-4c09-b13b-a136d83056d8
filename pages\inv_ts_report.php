<?php
session_start();
include 'db_connect.php';

if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Initial data load - Modified to fetch only credit entries
$username = $_SESSION['username'];
$stmt = $conn->prepare("SELECT * FROM transactions WHERE username = ? AND entry_type = 'credit' ORDER BY created_at DESC");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

// Calculate totals
$total_billed = 0;
$total_ts = 0;
$total_margin = 0;
$result->data_seek(0); // Reset pointer to beginning
while ($row = $result->fetch_assoc()) {
    $total_billed += $row['amount'];
    $total_ts += $row['entry_ts'];
    $total_margin += ($row['amount'] - $row['entry_ts']);
}
$result->data_seek(0); // Reset pointer again for main display
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Critical CSS -->
    <style>
        /* Critical styles that should load first */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow: hidden;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        #mainContent {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .report-container {
            opacity: 0;
            transition: opacity 0.3s ease-in;
        }
    </style>

    <!-- Deferred non-critical CSS -->
    <style>
        :root {
            --primary-blue: #2196F3;
            --light-blue: #E3F2FD;
            --hover-blue: #1976D2;
            --sky-blue: #87CEEB;
            --text-dark: #2c3e50;
            --border-color: #e0e0e0;
            --background: #F8FAFC;
        }

        .report-container {
            padding: 2rem;
            margin: 0 auto;
            max-width: 1400px;
            position: relative;
            margin-left: 0px;
            margin-top: 0px;
        }

        .header-section {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .page-title {
            color: var(--primary-blue);
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
        }

        .total-badge {
            background: var(--light-blue);
            color: var(--primary-blue);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow: hidden;
            margin: 0 auto;
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            table-layout: fixed;
        }

        .table thead th {
            background: var(--primary-blue);
            color: white;
            padding: 0.5rem;
            font-weight: 500;
            text-align: left;
            font-size: 0.95rem;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table tbody td {
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-dark);
            font-size: 0.95rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background: var(--background);
        }

        .table-footer {
            background: var(--light-blue);
            font-weight: 600;
            color: var(--primary-blue);
        }

        .table-footer td {
            padding: 0.5rem;
            text-align: right;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table-footer td:first-child {
            text-align: left;
            padding-left: 0.5rem;
        }

        /* Column widths */
        .table th:nth-child(1), .table td:nth-child(1) { width: 10%; } /* Entry Type */
        .table th:nth-child(2), .table td:nth-child(2) { width: 15%; } /* Customer */
        .table th:nth-child(3), .table td:nth-child(3) { width: 10%; } /* Docket No */
        .table th:nth-child(4), .table td:nth-child(4) { width: 10%; } /* Date */
        .table th:nth-child(5), .table td:nth-child(5) { width: 10%; } /* Mode */
        .table th:nth-child(6), .table td:nth-child(6) { width: 15%; } /* Destination */
        .table th:nth-child(7), .table td:nth-child(7) { width: 8%; } /* Weight */
        .table th:nth-child(8), .table td:nth-child(8) { width: 12%; } /* Billed Amount */
        .table th:nth-child(9), .table td:nth-child(9) { width: 12%; } /* TS Amount */
        .table th:nth-child(10), .table td:nth-child(10) { width: 12%; } /* Net Margin P/L */

        /* Amount columns alignment */
        .table th:nth-child(8),
        .table th:nth-child(9),
        .table th:nth-child(10),
        .table td:nth-child(8),
        .table td:nth-child(9),
        .table td:nth-child(10) {
            text-align: right;
            padding-right: 0.5rem;
        }

        @media (max-width: 1024px) {
            .report-container {
                padding: 1rem;
            }

            .header-section {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .header-section {
                flex-direction: column;
                align-items: stretch;
            }

            .total-badge {
                align-self: flex-start;
            }

            .table thead {
                display: none;
            }

            .table tbody td {
                display: block;
                padding: 0.5rem 1rem;
                text-align: right;
                border: none;
            }

            .table tbody tr {
                display: block;
                border-bottom: 1px solid var(--border-color);
                padding: 0.5rem 0;
            }

            .table td::before {
                content: attr(data-label);
                float: left;
                font-weight: 500;
                color: var(--primary-blue);
            }

            .table-footer td {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Main content container -->
    <div id="mainContent">
        <div class="report-container">
            <div class="header-section">
                <h1 class="page-title">Invoice & TS Report (FSC and GST not included)</h1>
                <div class="total-badge">
                    <i class="fas fa-file-alt"></i>
                    <span>Total Records: <span id="recordCount"><?php echo $result->num_rows; ?></span></span>
                </div>
            </div>

            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Entry Type</th>
                            <th>Customer</th>
                            <th>Docket No.</th>
                            <th>Date</th>
                            <th>Mode</th>
                            <th>Destination</th>
                            <th>Weight</th>
                            <th>Billed Amount</th>
                            <th>TS Amount</th>
                            <th>Net Margin P/L</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td data-label="entry type"><?php echo htmlspecialchars($row['entry_type']); ?></td>
                            <td data-label="customer"><?php echo htmlspecialchars($row['customer']); ?></td>
                            <td data-label="docket no"><?php echo htmlspecialchars($row['docket_no']); ?></td>
                            <td data-label="date"><?php echo date('d-M-Y', strtotime($row['docket_date'])); ?></td>
                            <td data-label="mode"><?php echo htmlspecialchars($row['mode_of_tsp']); ?></td>
                            <td data-label="destination"><?php echo htmlspecialchars($row['destination']); ?></td>
                            <td data-label="weight"><?php echo htmlspecialchars($row['weight']); ?> kg</td>
                            <td data-label="billed amount">₹<?php echo number_format($row['amount'], 2); ?></td>
                            <td data-label="ts amount">₹<?php echo number_format($row['entry_ts'], 2); ?></td>
                            <td data-label="net margin p/l">₹<?php echo number_format($row['amount'] - $row['entry_ts'], 2); ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-footer">
                            <td colspan="7"><strong>Grand Total:</strong></td>
                            <td><strong>₹<?php echo number_format($total_billed, 2); ?></strong></td>
                            <td><strong>₹<?php echo number_format($total_ts, 2); ?></strong></td>
                            <td><strong>₹<?php echo number_format($total_margin, 2); ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    $(document).ready(function() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const mainContent = document.getElementById('mainContent');
        const reportContainer = document.querySelector('.report-container');
        
        // Ensure the loading overlay is visible initially
        loadingOverlay.style.visibility = 'visible';
        loadingOverlay.style.opacity = '1';
        
        // Hide main content initially
        mainContent.style.visibility = 'hidden';
        mainContent.style.opacity = '0';
        
        // Function to show content
        function showContent() {
            // First hide the loading overlay
            loadingOverlay.style.opacity = '0';
            
            // After a brief delay, show the main content
            setTimeout(() => {
                // Hide loading overlay completely
                loadingOverlay.style.visibility = 'hidden';
                
                // Show main content
                mainContent.style.visibility = 'visible';
                mainContent.style.opacity = '1';
                document.body.style.overflow = 'auto'; // Re-enable scrolling
                
                // Show report container with a slight delay for smooth transition
                setTimeout(() => {
                    reportContainer.style.opacity = '1';
                }, 50);
            }, 300);
        }
        
        // Wait for everything to load
        if (document.readyState === 'complete') {
            showContent();
        } else {
            window.addEventListener('load', showContent);
        }
    });
    </script>
</body>
</html> 